package com.carplus.subscribe.service;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.model.PriceInfo;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoCriteriaRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MileageFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class PriceInfoServiceTest {

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void test() {
        System.out.println(new Gson().toJson(priceInfoService.cancelOrderCalculate("B11209270016")));
    }

    @Test
    public void test2() {
        Orders order = orderService.getOrder("B11209200020");
        System.out.println(new Gson().toJson(priceInfoService.calculateOrderPrice(order)));
    }

    @Test
    public void setReturnLate() {
        Orders order = orderService.getOrder("B11209240002");
        priceInfoService.setReturnLate(order);
    }


    @Test
    public void test3() {
        new OrderPriceInfoResponse(priceInfoService.get(27530));
    }

    @Test
    public void test4() throws JsonProcessingException {
        OrderPriceInfoCriteriaRequest request = new OrderPriceInfoCriteriaRequest();
//        request.setCategory(PriceInfoDefinition.PriceInfoCategory.ReturnLate);
        System.out.println(objectMapper.writeValueAsString(priceInfoService.getStageGroupPriceInfoByOrder("M202310184682", request)));
    }

    @Test
    public void test5() {
        System.out.println(priceInfoService.paidAmt("B11210050006"));
    }

    @Test
    public void test6() {
        priceInfoService.getUnPaidPriceInfoByOrder("B11210110027", true);
    }

    @Test
    public void test7() {
        priceInfoService.calculateMillageFee("B11210120021", 301445, 900, (Integer) null, null);
        priceInfoService.getUnPaidPriceInfoByOrder("B11210110027", true);
    }

    @Test
    public void test8() {
        int amt = priceInfoService.paidAmt("M202310177539");
        amt -= priceInfoService.refundAmt("M202310177539");
        System.out.println(amt);
    }

    @Test
    public void test9() {
        new OrderPriceInfoResponse(priceInfoService.get(47059));
    }


    @Test
    public void test10() {
        Orders orders = orderService.getOrder("B11207210583");
        int paidAmt = priceInfoService.paidRentAmt(orders.getOrderNo());
        if (orders.getStatus() != OrderStatus.CANCEL.getStatus() && orders.getStatus() != OrderStatus.CLOSE.getStatus() && orders.getStatus() != OrderStatus.ARRIVE_NO_CLOSE.getStatus()) {
            paidAmt -= priceInfoService.refundAmt(orders.getOrderNo());
            paidAmt -= priceInfoService.discountAmt(orders.getOrderNo());
        }
        System.out.println(paidAmt);
    }

    @Autowired
    private ContractLogic contractLogic;

    @Test
    public void test11() {
        Orders preOrder = contractLogic.getDepartOrdersByMainContract("U2023103101583");
        if (preOrder != null && StringUtils.isNotBlank(preOrder.getSrentalParentOrderNo())) {
            PriceInfo priceInfo = preOrder.getContract().getMainContract().getOriginalPriceInfo();
            AtomicReference<Double> mileageFee = new AtomicReference<>(priceInfo.getMileageFee() > 0 ? priceInfo.getMileageFee() : priceInfo.getOriginalMileageFee());
            priceInfoService.getPriceInfosByOrder(preOrder.getOrderNo(), MileageFee, Pay).forEach(
                opi -> {
                    double preMileage = Optional.ofNullable(opi).map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee).orElse(0d);
                    if (preMileage > 0 && preMileage < mileageFee.get()) {
                        mileageFee.set(preMileage);
                    }

                }
            );
            System.out.println(mileageFee.get());
        }
    }

    @Test
    public void refundAll(){
        priceInfoService.refundAll(75981,null);
    }
}
