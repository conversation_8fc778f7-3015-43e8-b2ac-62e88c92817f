package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.App;
import com.carplus.subscribe.db.mysql.dao.CarsRepository;
import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.dto.ETagInfoDTO;
import com.carplus.subscribe.db.mysql.dto.OrderPriceInfoDTO;
import com.carplus.subscribe.db.mysql.dto.OrdersDTO;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.enums.RenewType;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.authority.AdminUser;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.crs.CarBase;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.invoice.InvoiceRequest;
import com.carplus.subscribe.model.order.CashierOrderResponse;
import com.carplus.subscribe.model.order.MainContractResponse;
import com.carplus.subscribe.model.order.MainContractV2Response;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.request.contract.CancelRequest;
import com.carplus.subscribe.model.request.contract.ContractCriteria;
import com.carplus.subscribe.model.request.dropoff.CarDropOffCompleteRequest;
import com.carplus.subscribe.model.request.dropoff.CarDropOffDiscountRequest;
import com.carplus.subscribe.model.request.dropoff.CarDropOffRequest;
import com.carplus.subscribe.model.request.order.OrderUpdateRequest;
import com.carplus.subscribe.model.request.order.OrdersCriteria;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MileageFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@Slf4j
@SpringBootTest(classes = App.class)
@Transactional
class OrderServiceTest {

    @Autowired
    private OrderService orderService;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private CarsRepository carsRepository;
    @Autowired
    private OrderService mockOrderService;
    @Autowired
    private CarsService mockCarsService;
    @Autowired
    private ContractService mockContractService;
    @Autowired
    private CrsService mockCrsService;
    @Autowired
    private StationService stationService;

    @Test
    void test() {
        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setInvNo("AU82619855");
        orderService.searchByPage(new PageRequest(10, 0), criteria);
    }

    void dropOffCarConfirmForTest(String orderNo, int returnMileage, Date returnDate) {

        CarDropOffRequest dropOffRequest = new CarDropOffRequest();
        dropOffRequest.setReturnRemark("test");
        dropOffRequest.setReturnMileage(returnMileage);
        dropOffRequest.setReturnDate(returnDate);

        orderService.dropOffCarConfirm(orderNo, dropOffRequest, "K2765");

        Orders orderAfterConfirm = orderService.getOrder(orderNo);
        List<OrderPriceInfo> mileageFeeListAfterConfirm = priceInfoService.getPriceInfosByOrder(orderNo, MileageFee, Pay);

        assertNotNull(orderAfterConfirm.getEndDate());
        assertNotNull(orderAfterConfirm.getReturnMileage());
        assertNotNull(orderAfterConfirm.getReportMileage());
        mileageFeeListAfterConfirm.stream().map(OrderPriceInfo::getInfoDetail).forEach(infoDetail -> {
            assertNotNull(infoDetail.getStartMileage());
            assertNotNull(infoDetail.getEndMileage());
            assertNotNull(infoDetail.getTotalMileage());
        });
    }

    @Test
    void testDropOffCarConfirm() {

        String orderNo = "M202409101828";

        assertDoesNotThrow(() -> dropOffCarConfirmForTest(orderNo, 77777, new Date()));
    }

    @Test
    void testDropOffCarConfirm_success_StolenCar() {

        String orderNo = "M202411258512";

        Orders order = orderService.getOrder(orderNo);
        String plateNo = order.getPlateNo();
        Cars car = carsService.findByPlateNo(plateNo);

        assert order.getStatus() == OrderStatus.STOLEN.getStatus();
        assert Objects.equals(car.getCarStatus(), CarDefine.CarStatus.Stolen.getCode());

        assertDoesNotThrow(() -> dropOffCarConfirmForTest(orderNo, 63582, new Date()));
    }

    @Test
    void testDropOffCarConfirm_Failure_etagPaidAndActualReturnTimeEarlierThanEtagEndTime() {

        String orderNo = "M202409101828";

        ETagInfo etagInfo = eTagService.getLatestReturnETagInfo(orderService.getOrder(orderNo));
        Date returnDateEarlierThanEtagEndTime = Date.from(etagInfo.getReturnDate().minus(1, ChronoUnit.DAYS));

        Assertions.assertThrows(SubscribeException.class, () -> dropOffCarConfirmForTest(orderNo, 77777, returnDateEarlierThanEtagEndTime), "etag已收款，實際還車時間不可早於etag迄租時間");
    }

    @Test
    void testDropOffCarConfirm_success_etagPaidAndActualReturnTimeLaterThanEtagEndTime() {

        String orderNo = "M202409101828";

        ETagInfo etagInfo = eTagService.getLatestReturnETagInfo(orderService.getOrder(orderNo));
        Date returnDateLaterThanEtagEndTime = Date.from(etagInfo.getReturnDate().plus(30, ChronoUnit.MINUTES));

        assertDoesNotThrow(() -> dropOffCarConfirmForTest(orderNo, 77777, returnDateLaterThanEtagEndTime));
    }

    @Test
    void testUndoDropOffCarConfirm() throws JsonProcessingException {

        String orderNo = "M202502101166";

        Orders originalOrder = objectMapper.readValue(objectMapper.writeValueAsString(orderService.getOrder(orderNo)), Orders.class);
        List<OrderPriceInfo> originalOrderPriceInfoList = priceInfoService.getPriceInfosByOrder(orderNo).stream()
            .map(orderPriceInfo -> {
                try {
                    return objectMapper.readValue(objectMapper.writeValueAsString(orderPriceInfo), OrderPriceInfo.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }).collect(Collectors.toList());
        ETagInfo originalETagInfo = objectMapper.readValue(objectMapper.writeValueAsString(eTagService.getLatestNotReturnETagInfo(originalOrder, true)), ETagInfo.class);

        dropOffCarConfirmForTest(orderNo, 60239, new Date());

        orderService.undoDropOffCarConfirm(orderNo);

        OrdersDTO orderAfterUndoConfirm = OrdersDTO.convertToDto(orderService.getOrder(orderNo));
        List<OrderPriceInfoDTO> orderPriceInfoListAfterUndoConfirm = priceInfoService.getPriceInfosByOrder(orderNo).stream().map(OrderPriceInfoDTO::convertToDto).collect(Collectors.toList());
        ETagInfoDTO eTagInfoAfterUndoConfirm = ETagInfoDTO.convertToDto(eTagService.getLatestNotReturnETagInfo(originalOrder, true));

        assertThat(orderAfterUndoConfirm)
            .usingRecursiveComparison()
            .ignoringFields("createDate", "updateDate")
            .isEqualTo(originalOrder);
        assertThat(orderPriceInfoListAfterUndoConfirm)
            .usingRecursiveComparison()
            .ignoringFields("createDate", "updateDate")
            .isEqualTo(originalOrderPriceInfoList);
        assertThat(eTagInfoAfterUndoConfirm)
            .usingRecursiveComparison()
            .ignoringFields("createDate", "updateDate")
            .isEqualTo(originalETagInfo);
    }

    @Test
    void returnCar() {
        String orderNo = "M202403124461";
        CarDropOffCompleteRequest request = new CarDropOffCompleteRequest();
        request.setReturnMemberId("K2456");
        request.setPreownedStationCode("35");
        orderService.dropOffCar("K2456", orderNo, request);
    }

    @Test
    void dropOffCarDiscount() {
        String orderNo = "B11209240002";
        CarDropOffDiscountRequest dropOffRequest = new CarDropOffDiscountRequest();
        dropOffRequest.setAfterDiscountAmount(10000);
        dropOffRequest.setDiscount(7600);
        dropOffRequest.setCategory(PriceInfoDefinition.PriceInfoCategory.PayLate);
        dropOffRequest.setOriginAmount(17600);
        dropOffRequest.setManagerId("K2456");
        dropOffRequest.setPriceInfoPayId(27309);
        dropOffRequest.setReason("Test");
        dropOffRequest.setOriginAmount(5650);
        orderService.dropOffCarDiscount(orderNo, dropOffRequest, "K2456");
    }

    @Test
    void calculateStage() {
        Orders order = orderService.getOrder("B11209070005");
        DateUtil.calculateStageAndDate(order);
    }

    @Test
    void cancelOrder2() {
        CancelRequest cancelRequest = new CancelRequest();
        cancelRequest.setInvoices(new ArrayList<>());
        InvoiceRequest i = new InvoiceRequest();
        Invoice invoice = new Invoice();
        invoice.setTitle("測試");
        invoice.setType(2);
        invoice.setCategory(5);
        invoice.setId("L124074986");
        i.setAmount(25000);
        i.setInvoice(invoice);
        cancelRequest.getInvoices().add(i);
        cancelRequest.setRemark("test");
        orderService.cancelOrder("M202310209606", cancelRequest, "K2456");
    }

    @Test
    void recordRenewTypeToPreOrder() throws JsonProcessingException {
        orderService.recordRenewTypeToPreOrder("B11209280025", RenewType.RENEW);
    }

    @Test
    void dropOffCarEarlyView() throws JsonProcessingException {
        orderService.dropOffCarEarlyView("B11210020003");
    }

    @Test
    void generateOrderNo() throws JsonProcessingException {
        orderService.generateOrderNo();
    }

    @Test
    void isLastOrder() {
        Orders orders = orderService.getOrder("M202310253231");
        boolean test = orderService.isLastOrder(orders);
        System.out.println(test);
        orderService.refundSecurityDeposit(orders.getContract().getMainContract());
    }

    @Test
    void updateOrder() throws JsonProcessingException {
        String orderNo = "M202411129736";
        String orderUpdateRequestStr = "{\n" +
            "    \"expectEndDate\": 1739930400000,\n" +
            "    \"month\": 12,\n" +
            "    \"carReady\": null,\n" +
            "    \"plateNo\": \"RDH-3756\",\n" +
            "    \"carNo\": \"1837560408\",\n" +
            "    \"carLevel\": 35,\n" +
            "    \"departStationCode\": \"202\",\n" +
            "    \"returnStationCode\": \"202\",\n" +
            "    \"expectStartDate\": 1732068000000,\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"referInfo\": {\n" +
            "        \"name\": \"\",\n" +
            "        \"organization\": \"\"\n" +
            "    },\n" +
            "    \"invoice\": {\n" +
            "        \"type\": 2,\n" +
            "        \"category\": 5,\n" +
            "        \"carrierID\": null,\n" +
            "        \"id\": \"A195970863\",\n" +
            "        \"title\": \"王曉明\",\n" +
            "        \"defaultInvoiceType\": 2,\n" +
            "        \"companyId\": null,\n" +
            "        \"companyTitle\": null,\n" +
            "        \"donateCompanyId\": null,\n" +
            "        \"donateCompanyTitle\": null,\n" +
            "        \"printInvoice\": false\n" +
            "    },\n" +
            "    \"departMemberId\": null,\n" +
            "    \"returnMemberId\": null,\n" +
            "    \"custSource\": 2,\n" +
            "    \"remark\": null,\n" +
            "    \"companyDriver\": null,\n" +
            "    \"mileageDiscounts\": {},\n" +
            "    \"expectDepartDate\": 1732932000000\n" + // 改為 10 天後
            "}";
        OrderUpdateRequest orderUpdateRequest = objectMapper.readValue(orderUpdateRequestStr, OrderUpdateRequest.class);
        String companyCode = "carplus";
        String memberId = "K2765";
        AdminUser admin = null;
        List<MemberInfo> memberInfos = authorityServer.getMemberInfosNotRetired(memberId);
        if (!memberInfos.isEmpty()) {
            admin = new AdminUser();
            admin.setMemberId(memberId);
            admin.setCompanyCode(companyCode);
            admin.setMemberInfos(memberInfos);
            admin.setRoles(authorityServer.getAdminRoles(companyCode, memberId));
        }

        // 執行訂單更新
        orderService.updateOrder(orderNo, orderUpdateRequest, admin, memberId);

        // 取得更新後的訂單
        Orders updatedOrder = orderService.getOrder(orderNo);

        // 計算預期的迄租日期
        Stations returnStation = stationService.findByStationCode(orderUpdateRequest.getReturnStationCode());
        Instant expectedEndDate = DateUtil.calculateNewEndDate(orderUpdateRequest.getExpectDepartDate(), orderUpdateRequest.getMonth())
            .withHour(Integer.parseInt(returnStation.getEndHours().substring(0, 2)))
            .withMinute(Integer.parseInt(returnStation.getEndHours().substring(2, 4)))
            .withSecond(0)
            .toInstant();

        // 驗證更新後的迄租日期是否符合預期
        Assertions.assertEquals(
            expectedEndDate,
            updatedOrder.getExpectEndDate(),
            "新的預計迄租日期應為新起租日 + " + orderUpdateRequest.getMonth() + "個月 - 1天"
        );
    }

    /**
     * Get booking order which corresponding main contract is level discounted
     */
    private Orders getBookingOrderWithMainContractLevelDiscounted() {
        return orderService.getOrdersByStatus(OrderStatus.BOOKING).stream()
            .filter(o -> o.getContract().getMainContract().getOriginalPriceInfo().isLevelDiscounted()).findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
    }

    private Cars getCarByOrder(Orders order) {
        Cars car = carsService.findByPlateNo(order.getPlateNo());
        assert subscribeLevelService.isDiscountLevelEnabled(car);
        return car;
    }

    /**
     * Finds a new car with the same/different subscription level and specified discount level enabled status
     *
     * @param originalCar             The original car object
     * @param isSameSubscriptionLevel Whether the new car should have the same subscription level
     * @param discountLevelEnabled    Whether the discount level should be enabled
     * @return The plate number of the new car
     */
    private String findNewCarWithSameSubscriptionLevel(Cars originalCar, boolean isSameSubscriptionLevel, boolean discountLevelEnabled) {
        // Get the subscription level and plate number of the original car
        Integer originalCarSubscribeLevel = originalCar.getSubscribeLevel();
        String originalCarPlateNo = originalCar.getPlateNo();

        // Filter and find a new car that meets the criteria
        return carsRepository.findAll().stream()
            .filter(car -> car.getSubscribeLevel() != null
                && (isSameSubscriptionLevel == car.getSubscribeLevel().equals(originalCarSubscribeLevel))
                && !car.getIsDeleted()
                && !car.getPlateNo().equals(originalCarPlateNo)
                && (discountLevelEnabled == subscribeLevelService.isDiscountLevelEnabled(car)))
            .map(Cars::getPlateNo)  // Extract the plate number of the new car
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("No suitable car found"));  // Throw an exception if no suitable car is found
    }

    /**
     * Find a new car with the same subscription level as the original car's discount level
     */
    private String findCarWithSubscribeLevelSameAsOriginalCarDiscountLevel(Cars originalCar) {

        Integer originalCarSubscribeLevel = originalCar.getSubscribeLevel();
        Integer originalCarDiscountLevel = subscribeLevelService.findByLevel(originalCarSubscribeLevel).getDiscountLevel();

        return carsRepository.findAll().stream()
            .filter(car -> car.getSubscribeLevel() != null
                && car.getSubscribeLevel().equals(originalCarDiscountLevel)
                && !car.getIsDeleted()
                && !car.getPlateNo().equals(originalCar.getPlateNo()))
            .map(Cars::getPlateNo)
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("No suitable car found"));  // Throw an exception if no suitable car is found
    }

    @Test
    void validateSubscribeLevelWithMainContract_Success_ChangeCarWithSameSubscribeLevelAndDiscountLevelEnabled() {

        Orders order = getBookingOrderWithMainContractLevelDiscounted();

        MainContract mainContract = order.getContract().getMainContract();

        Cars originalCar = getCarByOrder(order);

        String newCarPlateNo = findNewCarWithSameSubscriptionLevel(originalCar, true, true);

        Assertions.assertDoesNotThrow(() -> orderService.validateSubscribeLevelWithMainContract(newCarPlateNo, mainContract));
    }

    @Test
    void validateSubscribeLevelWithMainContract_Failure_ChangeCarWithSameSubscribeLevelButDiscountLevelNotEnabled() {

        Orders order = getBookingOrderWithMainContractLevelDiscounted();

        MainContract mainContract = order.getContract().getMainContract();

        Cars originalCar = getCarByOrder(order);

        String newCarPlateNo = findNewCarWithSameSubscriptionLevel(originalCar, true, false);

        String expectedMessage = CAR_DISCOUNT_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg();

        BadRequestException exception = Assertions.assertThrows(
            BadRequestException.class,
            () -> orderService.validateSubscribeLevelWithMainContract(newCarPlateNo, mainContract)
        );

        Assertions.assertEquals(expectedMessage, exception.getReason());
    }

    @Test
    void validateSubscribeLevelWithMainContract_Failure_ChangeCarWithDifferentSubscribeLevelAndDiscountLevelNotEnabled() {
        Orders order = getBookingOrderWithMainContractLevelDiscounted();

        MainContract mainContract = order.getContract().getMainContract();

        Cars originalCar = getCarByOrder(order);

        String newCarPlateNo = findNewCarWithSameSubscriptionLevel(originalCar, false, false);

        String expectedMessage = CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg();

        BadRequestException exception = Assertions.assertThrows(
            BadRequestException.class,
            () -> orderService.validateSubscribeLevelWithMainContract(newCarPlateNo, mainContract)
        );

        Assertions.assertEquals(expectedMessage, exception.getReason());
    }

    @Test
    void validateSubscribeLevelWithMainContract_Failure_ChangeCarWithSubscribeLevelSameAsOriginalCarDiscountLevel() {

        Orders order = getBookingOrderWithMainContractLevelDiscounted();

        MainContract mainContract = order.getContract().getMainContract();

        Cars originalCar = getCarByOrder(order);

        String newCarPlateNo = findCarWithSubscribeLevelSameAsOriginalCarDiscountLevel(originalCar);

        String expectedMessage = CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg();

        BadRequestException exception = Assertions.assertThrows(
            BadRequestException.class,
            () -> orderService.validateSubscribeLevelWithMainContract(newCarPlateNo, mainContract)
        );

        Assertions.assertEquals(expectedMessage, exception.getReason());
    }

    /**
     * 測試車輛 <br/>
     * ['RDU-9613'(1,lv2), 'RDY-7835'(4,lv2), 'RCR-2953'(1,lv2)]
     * ['RAP-2156'(4,lv35), 'RBX-1787'(4,lv35),'RCR-5216'(1,lv35)]
     */
    @Test
    @DisplayName("測試出車中換車-前置條件檢核成功-BuId=4 換 BuId=4")
    void testReplaceCarCheckBeforeInternalServShouldSuccess() {
        Integer inCarStartMileage = 66710;
        Integer outCarEndMileage = 73000;
        List<String> lrContractReplaceCodes = new ArrayList<>();
        lrContractReplaceCodes.add("1");
        lrContractReplaceCodes.add("2");
        lrContractReplaceCodes.add("3");
        lrContractReplaceCodes.add("4");
        lrContractReplaceCodes.add("5");
        lrContractReplaceCodes.add("6");
        String lrContractMemo = "格上官網／新單／中古車\n" +
            "C2025092600082：2024/11/30 - 2025/11/29\n" +
            "M202411198700：2024/11/30 - 2025/02/27（租期3個月）\n";

        Cars outCar = new Cars();
        Cars inCar = new Cars();
        CarBaseInfoSearchResponse inCarCrsInfo = new CarBaseInfoSearchResponse();
        CarBaseInfoSearchResponse outCarCrsInfo = new CarBaseInfoSearchResponse();
        CarBase carBase = new CarBase();
        Orders order = new Orders();
        Contract contract = new Contract();
        MainContract mainContract = new MainContract();

        setMockInOutCars(outCar, inCar, inCarCrsInfo, outCarCrsInfo, carBase, order, contract, mainContract,
            inCarStartMileage, outCarEndMileage);

        List<String> messages = CarsUtil.replaceCarAudit(mainContract, order, lrContractReplaceCodes, lrContractMemo,
            inCar, inCarStartMileage, inCarCrsInfo, outCar, outCarEndMileage);
        log.info("{}", messages);
        Assertions.assertTrue(messages.isEmpty());
    }

    @Test
    @DisplayName("測試出車中換車-前置條件檢核失敗-BuId=4 換 BuId=4")
    void testReplaceCarCheckBeforeInternalServShouldFailed() {
        List<String> lrContractReplaceCodes = new ArrayList<>();
        String lrContractMemo = "";
        Integer inCarStartMileage = 66660;
        Integer outCarEndMileage = 73000;

        Cars outCar = new Cars();
        Cars inCar = new Cars();
        CarBaseInfoSearchResponse inCarCrsInfo = new CarBaseInfoSearchResponse();
        CarBaseInfoSearchResponse outCarCrsInfo = new CarBaseInfoSearchResponse();
        CarBase carBase = new CarBase();
        Orders order = new Orders();
        Contract contract = new Contract();
        MainContract mainContract = new MainContract();

        setMockInOutCars(outCar, inCar, inCarCrsInfo, outCarCrsInfo, carBase, order, contract, mainContract,
            inCarStartMileage, outCarEndMileage);

        List<String> messages = CarsUtil.replaceCarAudit(mainContract, order, lrContractReplaceCodes, lrContractMemo,
            inCar, inCarStartMileage, inCarCrsInfo, outCar, outCarEndMileage);
        log.info("{}", messages);
        assertFalse(messages.isEmpty());
    }

    private void setMockInOutCars(Cars outCar, Cars inCar, CarBaseInfoSearchResponse inCarCrsInfo, CarBaseInfoSearchResponse outCarCrsInfo,
                                  CarBase carBase, Orders order, Contract contract, MainContract mainContract,
                                  Integer inCarStartMileage, Integer outCarEndMileage) {
        String orderNo = "M202411198700";
        String inCarPlateNo = "RBX-1787";
        String outCarPlateNo = "RAP-2156";

        outCar.setPlateNo(outCarPlateNo);
        outCar.setCarStatus(CarDefine.CarStatus.BizOut.getCode());
        outCar.setCurrentMileage(outCarEndMileage);
        outCar.setSubscribeLevel(35);
        inCar.setPlateNo(inCarPlateNo);
        inCar.setCarStatus(CarDefine.CarStatus.Free.getCode());
        inCar.setCurrentMileage(inCarStartMileage);
        inCar.setLaunched(CarDefine.Launched.close);
        inCar.setSubscribeLevel(35);
        when(mockCarsService.findByPlateNo(outCarPlateNo)).thenReturn(outCar);
        when(mockCarsService.findByPlateNo(inCarPlateNo)).thenReturn(inCar);
        Optional.ofNullable(mockCarsService.findByPlateNo(outCarPlateNo)).orElseThrow(() -> new BadRequestException(String.format("查無汰換車車號 %s", outCarPlateNo)));
        Optional.ofNullable(mockCarsService.findByPlateNo(inCarPlateNo)).orElseThrow(() -> new BadRequestException(String.format("查無替代車車號 %s", inCarPlateNo)));
        inCarCrsInfo.setPlateNo(inCarPlateNo);
        inCarCrsInfo.setBuId(4);
        outCarCrsInfo.setPlateNo(outCarPlateNo);
        outCarCrsInfo.setBuId(4);
        carBase.setKm(inCarStartMileage);
        inCarCrsInfo.setCarBase(carBase);
        when(mockCrsService.getCar(inCarPlateNo)).thenReturn(inCarCrsInfo);
        mockCrsService.getCar(inCarPlateNo);
        order.setOrderNo(orderNo);
        order.setDepartMileage(inCarStartMileage);
        when(mockOrderService.getOrder(orderNo)).thenReturn(order);
        order = mockOrderService.getOrder(orderNo);
        contract.setMainContractNo("U2025092600076");
        when(mockContractService.getContractByContractNo(order.getContractNo())).thenReturn(contract);
        contract = mockContractService.getContractByContractNo(order.getContractNo());
        mainContract.setPlateNo(outCarPlateNo);
        mainContract.setStatus(1);
        when(mockContractService.getMainContractByNo(contract.getMainContractNo())).thenReturn(mainContract);
        mockContractService.getMainContractByNo(contract.getMainContractNo());
    }

    @Test
    void getCashierOrder_shouldReturnOrderWithRemarksSorted() {

        CashierOrderResponse cashierOrderResponse = orderService.getCashierOrder("M202501074527");
        assertNotNull(cashierOrderResponse);

        List<Remark> sortedRemarks = cashierOrderResponse.getRemarks();
        assertNotNull(sortedRemarks);
        assertFalse(sortedRemarks.isEmpty());

        if (sortedRemarks.size() > 1) {
            for (int i = 0; i < sortedRemarks.size() - 1; i++) {
                Remark current = sortedRemarks.get(i);
                Remark next = sortedRemarks.get(i + 1);

                // 驗證排序邏輯
                if (current.getCreateTime() == null) {
                    assertNull(next.getCreateTime(), "如果當前 createTime 為 null, 則下一個 createTime 也必須為 null");
                } else if (next.getCreateTime() != null) {
                    assertTrue(current.getCreateTime().after(next.getCreateTime()), "如果兩個 createTime 都不為 null, 則當前 createTime 必須晚於下一個 createTime");
                }
            }
        }
    }

    @Autowired
    private ContractService contractService;

    @Test
    public void getMainContractV2(){
        ContractCriteria contractCriteria = new ContractCriteria();
        contractCriteria.setDetail(true);
        contractCriteria.setMainContractNo("U2025042100005");
        Page<MainContractResponse> page = contractService.commonSearchPage(new PageRequest(5,0), contractCriteria);
        List<MainContractV2Response> result = page.getList().stream().map(MainContractV2Response::new).collect(Collectors.toList());
    }
}