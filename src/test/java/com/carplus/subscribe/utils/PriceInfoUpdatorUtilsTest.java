package com.carplus.subscribe.utils;

import com.carplus.subscribe.aspect.PriceInfoUpdatorTrackingAspect;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 測試 PriceInfoUpdatorUtils 的功能
 */
public class PriceInfoUpdatorUtilsTest {

    @Test
    public void testDeepCopyOrderPriceInfo() {
        // 創建原始 OrderPriceInfo
        OrderPriceInfo original = createTestOrderPriceInfo();
        
        // 執行深拷貝
        OrderPriceInfo copy = original.deepCopy();
        
        // 驗證基本欄位複製正確
        assertEquals(original.getId(), copy.getId());
        assertEquals(original.getOrderNo(), copy.getOrderNo());
        assertEquals(original.getAmount(), copy.getAmount());
        assertEquals(original.getCategory(), copy.getCategory());
        assertEquals(original.getUpdator(), copy.getUpdator());
        
        // 驗證 InfoDetail 深拷貝
        assertNotSame(original.getInfoDetail(), copy.getInfoDetail());
        assertEquals(original.getInfoDetail().getStartMileage(), copy.getInfoDetail().getStartMileage());
        assertEquals(original.getInfoDetail().getEndMileage(), copy.getInfoDetail().getEndMileage());
        assertEquals(original.getInfoDetail().getMileageFee(), copy.getInfoDetail().getMileageFee());
        assertEquals(original.getInfoDetail().getReason(), copy.getInfoDetail().getReason());
        
        // 修改原始對象，確保拷貝不受影響
        original.setAmount(9999);
        original.getInfoDetail().setStartMileage(8888);
        original.getInfoDetail().setReason("修改後的原因");
        
        // 驗證拷貝對象未受影響
        assertEquals(1000, copy.getAmount());
        assertEquals(100, copy.getInfoDetail().getStartMileage());
        assertEquals("測試原因", copy.getInfoDetail().getReason());
    }

    @Test
    public void testShouldUpdateUpdatorWithChanges() {
        OrderPriceInfo original = createTestOrderPriceInfo();
        OrderPriceInfo modified = original.deepCopy();
        
        // 修改金額
        modified.setAmount(2000);
        
        // 應該需要更新 updator
        assertTrue(PriceInfoUpdatorUtils.shouldUpdateUpdator("MEMBER123", original, modified));
    }

    @Test
    public void testShouldUpdateUpdatorWithoutChanges() {
        OrderPriceInfo original = createTestOrderPriceInfo();
        OrderPriceInfo unchanged = original.deepCopy();
        
        // 沒有修改任何內容
        
        // 不應該需要更新 updator
        assertFalse(PriceInfoUpdatorUtils.shouldUpdateUpdator("MEMBER123", original, unchanged));
    }

    @Test
    public void testShouldUpdateUpdatorWithInfoDetailChanges() {
        OrderPriceInfo original = createTestOrderPriceInfo();
        OrderPriceInfo modified = original.deepCopy();
        
        // 修改 InfoDetail 中的里程
        modified.getInfoDetail().setEndMileage(300);
        
        // 應該需要更新 updator
        assertTrue(PriceInfoUpdatorUtils.shouldUpdateUpdator("MEMBER123", original, modified));
    }

    @Test
    public void testShouldNotUpdateUpdatorWithoutMemberId() {
        OrderPriceInfo original = createTestOrderPriceInfo();
        OrderPriceInfo modified = original.deepCopy();
        
        // 修改金額
        modified.setAmount(2000);
        
        // 沒有 memberId，不應該更新 updator
        assertFalse(PriceInfoUpdatorUtils.shouldUpdateUpdator(null, original, modified));
        assertFalse(PriceInfoUpdatorUtils.shouldUpdateUpdator("", original, modified));
    }

    @Test
    public void testSetUpdatorIfChanged() {
        OrderPriceInfo original = createTestOrderPriceInfo();
        OrderPriceInfo modified = original.deepCopy();
        
        // 修改金額
        modified.setAmount(2000);
        
        // 執行條件性設定 updator
        PriceInfoUpdatorUtils.setUpdatorIfChanged(original, modified, "MEMBER123");
        
        // 驗證 updator 已設定
        assertEquals("MEMBER123", modified.getUpdator());
    }

    @Test
    public void testSetUpdatorIfNotChanged() {
        OrderPriceInfo original = createTestOrderPriceInfo();
        OrderPriceInfo unchanged = original.deepCopy();

        // 沒有修改任何內容

        // 執行條件性設定 updator
        PriceInfoUpdatorUtils.setUpdatorIfChanged(original, unchanged, "MEMBER123");

        // 驗證 updator 未被設定
        assertEquals("ORIGINAL_UPDATOR", unchanged.getUpdator());
    }

    @Test
    public void testAOPTrackingHelperMethods() {
        OrderPriceInfo orderPriceInfo = createTestOrderPriceInfo();

        // 測試記錄原始狀態
        PriceInfoTrackingHelper.recordBeforeUpdate(orderPriceInfo);

        // 修改價格資訊
        orderPriceInfo.setAmount(2000);

        // 測試應用追蹤
        PriceInfoTrackingHelper.applyAfterUpdate(orderPriceInfo);

        // 測試新建記錄追蹤
        OrderPriceInfo newRecord = createTestOrderPriceInfo();
        newRecord.setId(null); // 模擬新建記錄
        PriceInfoTrackingHelper.applyForNewRecord(newRecord);
    }

    private OrderPriceInfo createTestOrderPriceInfo() {
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setId(1);
        orderPriceInfo.setOrderNo("M202412010001");
        orderPriceInfo.setStage(1);
        orderPriceInfo.setAmount(1000);
        orderPriceInfo.setCategory(PriceInfoDefinition.PriceInfoCategory.MileageFee);
        orderPriceInfo.setType(1);
        orderPriceInfo.setUpdator("ORIGINAL_UPDATOR");
        orderPriceInfo.setLastPayDate(Instant.now());
        orderPriceInfo.setReceivableDate(Instant.now());
        
        PriceInfoDetail detail = new PriceInfoDetail();
        detail.setStartMileage(100);
        detail.setEndMileage(200);
        detail.setMileageFee(5.0);
        detail.setReason("測試原因");
        detail.setAdminId("ADMIN123");
        
        orderPriceInfo.setInfoDetail(detail);
        
        return orderPriceInfo;
    }
}
