package com.carplus.subscribe.controller.contract;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class ContractPublicControllerTest {

    @Autowired
    private MockMvc mockMvc;

    private long getExpectStartDate() {
        return LocalDateTime.now(DateUtils.ZONE_TPE)
            .plusDays(7)
            .atZone(DateUtils.ZONE_TPE)
            .toInstant()
            .toEpochMilli();
    }

    @Test
    void shouldCreateContract_ExternalApi_CheckSysRemarker() throws Exception {
        // Arrange: Prepare contract creation JSON
        String createContractJson = "{\n" +
            "    \"plateNo\": \"RDU-8122\",\n" + // Assuming RDU-8122 has cnDesc
            "    \"carLevel\": 9,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"returnStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + getExpectStartDate() + ",\n" +
            "    \"month\": 12,\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"invoice\": {\n" +
            "        \"category\": 5,\n" +
            "        \"id\": \"A193477449\",\n" +
            "        \"title\": \"李志宏\",\n" +
            "        \"type\": 2\n" +
            "    },\n" +
            "    \"custSource\": 2,\n" +
            "    \"custRemark\": \"客戶備註測試\",\n" +
            "    \"merchList\": []\n" + // Empty merchList
            "}";

        // Act and Assert: Use the custom ResultMatcher for the cnDesc remark
        mockMvc.perform(post("/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914) // acctId in header
                .content(createContractJson))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.mainContract.plateNo").value("RDU-8122"))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem("客戶備註測試")))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(allOf(containsString("原始收訂車輛"), containsString("車體描述")))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].remarkerName", hasItem("sys")));
    }
}