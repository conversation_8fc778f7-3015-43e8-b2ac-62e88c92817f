package com.carplus.subscribe.feign;

import com.carplus.subscribe.model.insurance.policy.PolicyListResponse;
import com.carplus.subscribe.service.InsuranceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class InsuranceClientTest {

    @Autowired
    private InsuranceService insuranceService;

    @Test
    void queryPolicy() {
        List<PolicyListResponse> policyListResponse = insuranceService.queryValidArbitraryPolicies("RAS-3225");
        System.out.println(policyListResponse);
    }

}