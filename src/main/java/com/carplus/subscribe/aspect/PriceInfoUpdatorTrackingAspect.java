package com.carplus.subscribe.aspect;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.annotation.TrackPriceInfoUpdator;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.utils.PriceInfoUpdatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 價格資訊更新人員追蹤切面
 * 
 * 自動攔截標記了 @TrackPriceInfoUpdator 註解的方法，
 * 提取 memberId 並追蹤 OrderPriceInfo 的變更
 */
@Aspect
@Component
@Order(1) // 確保在事務切面之後執行
@Slf4j
public class PriceInfoUpdatorTrackingAspect {

    /**
     * 線程本地存儲，用於在方法執行過程中傳遞 memberId
     */
    private static final ThreadLocal<String> MEMBER_ID_CONTEXT = new ThreadLocal<>();
    
    /**
     * 線程本地存儲，用於追蹤方法執行前的 OrderPriceInfo 狀態
     */
    private static final ThreadLocal<Map<Integer, OrderPriceInfo>> ORIGINAL_PRICE_INFO_CONTEXT = new ThreadLocal<>();

    /**
     * 攔截標記了 @TrackPriceInfoUpdator 註解的方法
     */
    @Around("@annotation(trackPriceInfoUpdator)")
    public Object trackPriceInfoUpdator(ProceedingJoinPoint joinPoint, TrackPriceInfoUpdator trackPriceInfoUpdator) throws Throwable {
        String memberId = extractMemberIdFromParameters(joinPoint);
        
        // 驗證 memberId
        if (StringUtils.isBlank(memberId)) {
            if (trackPriceInfoUpdator.required()) {
                log.warn("方法 {} 標記為需要 memberId 但未找到有效的 memberId", joinPoint.getSignature().toShortString());
            } else {
                log.debug("方法 {} 未提供 memberId，跳過 updator 追蹤", joinPoint.getSignature().toShortString());
            }
            // 即使沒有 memberId 也繼續執行方法
            return joinPoint.proceed();
        }

        // 設定上下文
        MEMBER_ID_CONTEXT.set(memberId);
        ORIGINAL_PRICE_INFO_CONTEXT.set(new ConcurrentHashMap<>());
        
        try {
            log.debug("開始追蹤價格資訊更新，方法: {}, memberId: {}, 描述: {}", 
                     joinPoint.getSignature().toShortString(), memberId, trackPriceInfoUpdator.description());
            
            // 執行原方法
            Object result = joinPoint.proceed();
            
            log.debug("完成價格資訊更新追蹤，方法: {}", joinPoint.getSignature().toShortString());
            
            return result;
        } finally {
            // 清理上下文
            MEMBER_ID_CONTEXT.remove();
            ORIGINAL_PRICE_INFO_CONTEXT.remove();
        }
    }

    /**
     * 從方法參數中提取 memberId
     */
    private String extractMemberIdFromParameters(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            
            // 查找標記了 @RequestHeader 且 name 為 AUTH_HEADER_MEMBER 的參數
            RequestHeader requestHeader = parameter.getAnnotation(RequestHeader.class);
            if (requestHeader != null && 
                CarPlusConstant.AUTH_HEADER_MEMBER.equals(requestHeader.name()) &&
                args[i] instanceof String) {
                return (String) args[i];
            }
        }
        
        return null;
    }

    /**
     * 獲取當前線程的 memberId
     */
    public static String getCurrentMemberId() {
        return MEMBER_ID_CONTEXT.get();
    }

    /**
     * 記錄 OrderPriceInfo 的原始狀態
     * 在 OrderPriceInfo 被修改前調用此方法
     */
    public static void recordOriginalState(OrderPriceInfo orderPriceInfo) {
        Map<Integer, OrderPriceInfo> originalStates = ORIGINAL_PRICE_INFO_CONTEXT.get();
        if (originalStates != null && orderPriceInfo != null && orderPriceInfo.getId() != null) {
            // 只記錄一次原始狀態
            originalStates.computeIfAbsent(orderPriceInfo.getId(), k -> orderPriceInfo.deepCopy());
        }
    }

    /**
     * 應用 updator 追蹤到 OrderPriceInfo
     * 在 OrderPriceInfo 被修改後調用此方法
     */
    public static void applyUpdatorTracking(OrderPriceInfo orderPriceInfo) {
        String memberId = getCurrentMemberId();
        if (StringUtils.isBlank(memberId) || orderPriceInfo == null) {
            return;
        }

        Map<Integer, OrderPriceInfo> originalStates = ORIGINAL_PRICE_INFO_CONTEXT.get();
        if (originalStates == null) {
            return;
        }

        if (orderPriceInfo.getId() != null) {
            // 更新現有記錄
            OrderPriceInfo originalState = originalStates.get(orderPriceInfo.getId());
            PriceInfoUpdatorUtils.setUpdatorIfChanged(originalState, orderPriceInfo, memberId);
        } else {
            // 新建記錄
            PriceInfoUpdatorUtils.setUpdator(orderPriceInfo, memberId);
        }
    }

    /**
     * 批量應用 updator 追蹤到 OrderPriceInfo 列表
     */
    public static void applyUpdatorTracking(List<OrderPriceInfo> orderPriceInfos) {
        if (orderPriceInfos != null) {
            orderPriceInfos.forEach(PriceInfoUpdatorTrackingAspect::applyUpdatorTracking);
        }
    }
}
