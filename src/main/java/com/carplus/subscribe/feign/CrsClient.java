package com.carplus.subscribe.feign;

import carplus.common.response.Result;
import com.carplus.subscribe.model.crs.*;
import com.carplus.subscribe.model.crs.assign.*;
import com.carplus.subscribe.model.station.CarAreaInfoListWithCount;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Service
@FeignClient(contextId = "CrsService", name = "crs", url = "${carplus.service.crs}")
public interface CrsClient {

    @RequestMapping(value = "/internal/crs/v1/carBaseInfo/search/car/list", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<CarBaseInfoSearchPage> getCars(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestBody CarBaseInfoQueryReq carBaseInfoQueryReq);

    @RequestMapping(value = "/internal/crs/v1/carBaseInfo/search/carBaseList", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<List<CarBaseInfoQueryResponse>> getCarBaseList(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestBody CarBaseQueryReq carBaseQueryReq);

    @RequestMapping(value = "/internal/crs/v1/carBaseInfo/updKm", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<List<CarBaseUpdateKmResponse>> updateKm(@RequestBody List<CarBaseUpdateKmReq> carBaseUpdateKmReqs);

    @RequestMapping(value = "/internal/crs/v1/assign/addAssign", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<Integer> changeBu(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestBody AssignAddReq assignAddReq);

    @RequestMapping(value = "/internal/crs/v1/change/getBuChange/{changeId}", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<ChangeListResponse> getChangeBuInfos(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @PathVariable Integer changeId);

    @RequestMapping(value = "/internal/crs/v1/BUChange/list", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<List<BUChangeSearchResponse>> getChangeBuInfoDetails(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestBody BUChangeSearchReq buChangeSearchReq);


    @RequestMapping(value = "internal/crs/v1/businessPurchase/order/searchProjectCar", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<List<PurchaseProjectCarSearchResponse>> searchProjectCar(@RequestBody PurchaseProjectCarSearchRequest purchaseProjectCarSearchRequest);

    /**
     * 取消出車申請
     */
    @RequestMapping(value = "/internal/crs/v1/carMove/leaveCancel", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<?> leaveCancel(@RequestBody UnMoveCarReq unMoveCarReq);

    /**
     * 取消撥車申請
     */
    @RequestMapping(value = "/internal/crs/v1/assign/cancelChange", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<?> cancelChange(@RequestBody BUChangeCancelReq buChangeCancelReq);

    /**
     * 車輛管制
     */
    @RequestMapping(value = "internal/crs/v1/carControl/batch", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<?> carControl(@RequestBody List<CarControlSaveRequest> carControlSaveRequest);

    /**
     * 領牌日查詢
     */
    @RequestMapping(value = "internal/crs/v1/carLicense/search/LicenseDate", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<List<CarLicenseResponse>> getLicenseDate(@RequestBody PurchaseProjectCarSearchRequest purchaseProjectCarSearchRequest);

    /**
     * 中古車區域清單
     */
    @RequestMapping(value = "internal/crs/v1/receive/area", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<CarAreaInfoListWithCount> getReceiveArea(@RequestParam Integer buId);

    /**
     * 中古車停放點清單
     */
    @RequestMapping(value = "internal/crs/v1/receive/location", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<CarAreaInfoListWithCount> getReceiveLocation(@RequestParam Integer buId, @RequestParam String areaCode);
}
