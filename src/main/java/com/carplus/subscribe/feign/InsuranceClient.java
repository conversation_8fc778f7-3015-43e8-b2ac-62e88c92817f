package com.carplus.subscribe.feign;

import carplus.common.response.Result;
import com.carplus.subscribe.model.insurance.InsurePlanListRequest;
import com.carplus.subscribe.model.insurance.InsurePlanSearchResultResponse;
import com.carplus.subscribe.model.insurance.RequisitionCreateRequest;
import com.carplus.subscribe.model.insurance.policy.PolicyListRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import static com.carplus.subscribe.constant.CarPlusConstant.AUTH_HEADER_MEMBER;
import static com.carplus.subscribe.constant.CarPlusConstant.HEADER_COMPANY_CODE;

@Service
@FeignClient(contextId = "InsuranceService", name = "insurance", url = "${carplus.service.insurance}")
public interface InsuranceClient {

    /**
     * 批次投保單暫存資料
     */
    @RequestMapping(value = "/internal/carinsurance/v1/requisitions_batch", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<?> addBatchInsurance(@RequestHeader(HEADER_COMPANY_CODE) String companyId, @RequestHeader(AUTH_HEADER_MEMBER) String memberId, @RequestBody List<RequisitionCreateRequest> requisitionCreateRequests);

    /**
     * 查詢最適保險方案
     */
    @RequestMapping(value = "/internal/carinsurance/v1/plan/insurePlanList", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<List<InsurePlanSearchResultResponse>> searchInsurePlanList(@RequestBody List<InsurePlanListRequest> requisitionCreateRequests);

    /**
     * 保單、退保查詢列表
     */
    @RequestMapping(value = "/internal/carinsurance/v1/policy", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<?> queryPolicy(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @SpringQueryMap PolicyListRequest policyListRequest);
}
