package com.carplus.subscribe.db.mysql.dao;

import carplus.common.redis.cache.Del;
import carplus.common.redis.cache.DelAll;
import carplus.common.redis.cache.Get;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.carplus.subscribe.model.invoice.InvoiceDTO;
import com.carplus.subscribe.model.request.invoice.InvoiceCriteriaRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class InvoicesRepository extends SimpleJpaRepository<Invoices, String> {

    private final EntityManager em;

    public InvoicesRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Invoices.class, em);
        this.em = em;
    }

    @NonNull
    @Get(group = Invoices.class, key = "'invoice.' + #orderNo", ttl = 60 * 20)
    public List<Invoices> findInvoicesByOrderNo(@NonNull String orderNo) {
        return findAll((root, query, builder) ->
            builder.equal(root.get("orderNo"), orderNo));
    }

    @NonNull
    public List<Invoices> findInvoicesByOrderNos(@NonNull List<String> orderNos) {
        return findAll((root, query, builder) ->
            root.get("orderNo").in(orderNos));
    }

    @Transactional
    @Override
    @Del(group = Invoices.class, key = "'invoice.' + #entity.orderNo")
    public <S extends Invoices> S save(S entity) {
        return super.save(entity);
    }

    @Transactional
    @Override
    @Del(group = Invoices.class, key = "'invoice.' + #entity.orderNo")
    public <S extends Invoices> S saveAndFlush(S entity) {
        return super.saveAndFlush(entity);
    }

    @Transactional
    @Override
    @DelAll(group = Invoices.class)
    public <S extends Invoices> List<S> saveAll(Iterable<S> entities) {
        return super.saveAll(entities);
    }

    public List<Invoices> findInvoicesNeedCheckout() {
        return findAll((root, query, builder) ->
            builder.and(root.get("isCheckout").in(Arrays.asList(InvoiceDefine.InvCheckoutStatus.CHECKOUT.getStatus(), InvoiceDefine.InvCheckoutStatus.MULTIPLE_CHECKOUT.getStatus()))));
    }

    public List<Invoices> findInvoicesByOrderNosWithoutCA(List<String> orderNos) {
        // 排除 InvCategory.CA (自然人憑證)
        return findAll((root, query, builder) -> {
            query.orderBy(builder.desc(root.get("createdAt")));
            return builder.and(
                builder.in(root.get("orderNo")).value(orderNos),
                builder.notEqual(
                    builder.function(
                        "json_extract",
                        String.class,
                        root.get("invoice"),
                        builder.literal("$.category")
                    ),
                    builder.literal(InvoiceDefine.InvCategory.CA.getCategory())
                )
            );
        });
    }

    public List<InvoiceDTO> getInvoices(InvoiceCriteriaRequest criteria) {
        return getInvoices(criteria, null, null);
    }

    private List<InvoiceDTO> getInvoices(InvoiceCriteriaRequest criteria, Integer skip, Integer limit) {
        String createdInvoices = "SELECT new com.carplus.subscribe.model.invoice.InvoiceDTO(i.invNo, i.orderNo, '" + InvoiceDefine.InvStatus.CREATE.name()
            + "', i.invoice, i.createdAt, i.deletedAt, i.unTax, i.tax, i.amount) "
            + "FROM Invoices i "
            + "WHERE i.createdAt BETWEEN :dateFrom AND :dateTo";

        String deletedInvoices = "SELECT new com.carplus.subscribe.model.invoice.InvoiceDTO(i.invNo, i.orderNo, i.status, i.invoice, i.createdAt, i.deletedAt, "
            + "(i.unTax * -1), (i.tax * -1), (i.amount * -1)) "
            + "FROM Invoices i "
            + "WHERE i.deletedAt BETWEEN :dateFrom AND :dateTo";

        TypedQuery<InvoiceDTO> createdInvoicesQuery = em.createQuery(createdInvoices, InvoiceDTO.class);
        createdInvoicesQuery.setParameter("dateFrom", Date.from(criteria.getDateFrom()));
        createdInvoicesQuery.setParameter("dateTo", Date.from(criteria.getDateTo()));

        TypedQuery<InvoiceDTO> deletedInvoicesQuery = em.createQuery(deletedInvoices, InvoiceDTO.class);
        deletedInvoicesQuery.setParameter("dateFrom", Date.from(criteria.getDateFrom()));
        deletedInvoicesQuery.setParameter("dateTo", Date.from(criteria.getDateTo()));

        List<InvoiceDTO> createdInvoicesList = createdInvoicesQuery.getResultList();
        List<InvoiceDTO> deletedInvoicesList = deletedInvoicesQuery.getResultList();

        List<InvoiceDTO> results = new ArrayList<>(createdInvoicesList);
        results.addAll(deletedInvoicesList);

        if (skip != null) {
            results = results.subList(skip, Math.min(results.size(), skip + (limit == null ? Integer.MAX_VALUE : limit)));
        }

        if (limit != null) {
            results = results.subList(0, Math.min(results.size(), limit));
        }

        return results.stream().sorted(Comparator.comparing(InvoiceDTO::getInvNo)).collect(Collectors.toList());
    }
}