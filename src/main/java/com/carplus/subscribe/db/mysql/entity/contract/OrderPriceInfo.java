package com.carplus.subscribe.db.mysql.entity.contract;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 訂單費用資訊
 */
@FieldNameConstants
@Entity(name = "order_price_info")
@Data
public class OrderPriceInfo extends GeneralEntity implements PriceInfoInterface {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 訂單編號
     */
    private String orderNo;

    /**
     * 期數
     */
    private Integer stage;

    /**
     * 最後付款期限
     */
    private Instant lastPayDate;

    /**
     * 可開始收款日期
     */
    private Instant receivableDate;

    /**
     * 費用類別
     */
    @Enumerated(EnumType.STRING)
    private PriceInfoDefinition.PriceInfoCategory category;

    @Type(type = "json")
    private PriceInfoDetail infoDetail;

    /**
     * 周邊商品代號
     */
    private String skuCode;

    /**
     * 匯款登打ID
     */
    @Type(type = "json")
    private List<Long> remitAccountIds;

    /**
     * 費用型態
     */
    private int type;

    /**
     * 費用
     */
    private Integer amount = 0;

    /**
     * 已收費用
     */
    private Integer receivedAmount = 0;

    /**
     * recTradeId編號
     */
    private String recTradeId;

    /**
     * refundId 退款編號
     */
    private String refundId;

    /**
     * payment編號
     */
    private Integer paymentId;

    /**
     * 關聯費用資訊
     */
    private Integer refPriceInfoNo;

    /**
     * 群組編號，審核用
     */
    private String uid;

    /**
     * 對應的
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(referencedColumnName = "id", name = "refPriceInfoNo", insertable = false, updatable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private OrderPriceInfo refPriceInfo;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.DETACH)
    @JoinColumn(referencedColumnName = "orderNo", name = "orderNo", insertable = false, updatable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private Orders order;

    @PrePersist
    private void prePersist() {
        if (refPriceInfo != null && refPriceInfo.getId() != null && refPriceInfoNo == null) {
            refPriceInfoNo = refPriceInfo.getId();
        }
    }

    public void setLastPayDate(Instant lastPayDate) {
        if (receivableDate == null) {
            this.lastPayDate = lastPayDate;
            this.receivableDate = DateUtil.convertToStartOfInstant(this.lastPayDate.minus(11, ChronoUnit.DAYS));
        } else {
            this.lastPayDate = lastPayDate;
        }
    }

    public boolean isValidRefOrderPriceInfo() {
        return (getType() == PriceInfoDefinition.PriceInfoType.Refund.getCode() || getType() == PriceInfoDefinition.PriceInfoType.Discount.getCode())
            && Optional.ofNullable(getInfoDetail()).map(detail -> (detail.getIsAgree() == null || detail.getIsAgree()) && StringUtils.isNotBlank(getUid()))
            .orElse(Objects.isNull(getUid()));
    }

    public boolean isMerchandise() {
        return PriceInfoDefinition.PriceInfoCategory.Merchandise == getCategory() && StringUtils.isNotBlank(getSkuCode());
    }
}
