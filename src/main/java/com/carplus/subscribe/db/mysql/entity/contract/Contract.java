package com.carplus.subscribe.db.mysql.entity.contract;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.model.PriceInfo;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@FieldNameConstants
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "contractNo")
@Entity(name = "contract")
@Data
public class Contract extends GeneralEntity implements EContractReferencable {
    /**
     * 合約編號
     */
    @Id
    private String contractNo;

    /**
     * 主合約編號
     */
    private String mainContractNo;

    /**
     * 合約狀態
     */
    private Integer status;

    /**
     * 前約編號
     */
    private String parentContractNo;

    /**
     * 續約第幾期
     */
    private Integer stage;

    /**
     * 預計開始時間
     */
    @Column(name = "expectStartDate")
    private Instant expectStartDate;

    /**
     * 實際開始時間
     */
    @Column(name = "startDate")
    private Instant startDate;

    /**
     * 預計結束時間
     */
    @Column(name = "expectEndDate")
    private Instant expectEndDate;

    /**
     * 實際結束時間
     */
    @Column(name = "endDate")
    private Instant endDate;

    /**
     * 出車單號
     */
    @Column(name = "formNo")
    private String formNo;

    /**
     * 是否保免責險
     */
    private Boolean disclaimer = false;

    /**
     * 是否投保溢價險
     */
    private Boolean premium = false;

    /**
     * 是否用代步車
     */
    private Boolean replacement = false;

    /**
     * 出車人員編號
     */
    @Deprecated
    @Column(name = "departMemberId")
    private String departMemberId;

    /**
     * 還車人員編號
     */
    @Deprecated
    @Column(name = "returnMemberId")
    private String returnMemberId;

    /**
     * 出車任務編號
     */
    private String departTaskId;

    /**
     * 還車任務編號
     */
    private String returnTaskId;

    /**
     * 合約建立時電子合約版本
     */
    private String eContractTempVerId;

    /**
     * 電子合約編號
     */
    private String eContractId;

    @OneToMany(mappedBy = "contract")
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private List<Orders> orders;

    @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.REFRESH)
    @JoinColumn(referencedColumnName = "mainContractNo", name = "mainContractNo", insertable = false, updatable = false)
    private MainContract mainContract;

    // --- EContractReferencable Implementation ---

    @JsonIgnore
    @Override
    public String getEntityNo() {
        return this.contractNo;
    }

    @JsonIgnore
    @Override
    public String getPlateNo() {
        return Optional.ofNullable(this.mainContract).map(MainContract::getPlateNo).orElse(null);
    }

    @JsonIgnore
    @Override
    public String getDepartStationCode() {
        return Optional.ofNullable(this.mainContract).map(MainContract::getDepartStationCode).orElse(null);
    }

    @JsonIgnore
    @Override
    public String getReturnStationCode() {
        return Optional.ofNullable(this.mainContract).map(MainContract::getReturnStationCode).orElse(null);
    }

    @JsonIgnore
    @Override
    public Integer getAcctId() {
        return Optional.ofNullable(this.mainContract).map(MainContract::getAcctId).orElse(null);
    }

    @JsonIgnore
    @Override
    public int getSecurityDeposit() {
        return Optional.ofNullable(mainContract).map(MainContract::getOriginalPriceInfo).map(PriceInfo::getSecurityDepositInfo).map(SecurityDepositInfo::getSecurityDeposit).orElse(-99999);
    }

    @JsonIgnore
    @Override
    public int getMonthlyFee() {
        return Optional.ofNullable(this.mainContract).map(MainContract::getOriginalPriceInfo).map(PriceInfo::getUseMonthlyFee).orElse(-99999);
    }

    @JsonIgnore
    @Override
    public double getMileageFee() {
        return Optional.ofNullable(this.mainContract).map(MainContract::getOriginalPriceInfo).map(PriceInfo::getMileageFee).orElse(-99999.0);
    }
}
