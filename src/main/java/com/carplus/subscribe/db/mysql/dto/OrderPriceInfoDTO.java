package com.carplus.subscribe.db.mysql.dto;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.Instant;
import java.util.List;

@Data
public class OrderPriceInfoDTO {

    private Integer id;
    private String orderNo;
    private Integer stage;
    private Instant lastPayDate;
    private Instant receivableDate;
    @Enumerated(EnumType.STRING)
    private PriceInfoDefinition.PriceInfoCategory category;
    private int type;
    private Integer amount;
    private Integer receivedAmount;
    private PriceInfoDetail infoDetail;
    private List<Long> remitAccountIds;
    private Integer paymentId;
    private String recTradeId;
    private String refundId;
    private Integer refPriceInfoNo;
    private String uid;
    private String updator;

    public static OrderPriceInfoDTO convertToDto(OrderPriceInfo orderPriceInfo) {
        OrderPriceInfoDTO dto = new OrderPriceInfoDTO();
        BeanUtils.copyProperties(orderPriceInfo, dto);
        return dto;
    }

    public OrderPriceInfo revertToEntity() {
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        BeanUtils.copyProperties(this, orderPriceInfo);
        return orderPriceInfo;
    }
}
