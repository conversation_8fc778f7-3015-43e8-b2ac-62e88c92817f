package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.request.sku.SkuCriteria;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Repository
public class SkuRepository extends SimpleJpaRepository<Sku, String> {

    private final EntityManager em;

    public SkuRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Sku.class, em);
        this.em = em;
    }

    public long count(SkuCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<Sku> root = cq.from(Sku.class);

        List<Predicate> predicates = buildPredicates(criteria, root, cb);

        cq.select(cb.count(root));
        if (!predicates.isEmpty()) {
            cq.where(predicates.toArray(new Predicate[0]));
        }

        return em.createQuery(cq).getSingleResult();
    }

    public List<Sku> search(SkuCriteria criteria, Integer limit, Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Sku> cq = cb.createQuery(Sku.class);
        Root<Sku> root = cq.from(Sku.class);

        List<Predicate> predicates = buildPredicates(criteria, root, cb);

        if (!predicates.isEmpty()) {
            cq.where(predicates.toArray(new Predicate[0]));
        }

        // 預設按照 code 排序
        cq.orderBy(cb.asc(root.get(Sku.Fields.code)));

        TypedQuery<Sku> query = em.createQuery(cq);
        query.setFirstResult(offset);
        query.setMaxResults(limit);

        return query.getResultList();
    }

    private List<Predicate> buildPredicates(SkuCriteria criteria, Root<Sku> root, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();

        if (StringUtils.isNotBlank(criteria.getCode())) {
            predicates.add(cb.equal(root.get(Sku.Fields.code), criteria.getCode()));
        }

        if (StringUtils.isNotBlank(criteria.getType())) {
            predicates.add(cb.equal(root.get(Sku.Fields.type), criteria.getType()));
        }

        if (StringUtils.isNotBlank(criteria.getName())) {
            predicates.add(cb.like(root.get(Sku.Fields.name), "%" + criteria.getName() + "%"));
        }

        if (criteria.getIsOfficial() != null) {
            predicates.add(cb.equal(root.get(Sku.Fields.isOfficial), criteria.getIsOfficial()));
        }

        if (criteria.getIsCashier() != null) {
            predicates.add(cb.equal(root.get(Sku.Fields.isCashier), criteria.getIsCashier()));
        }

        return predicates;
    }

    public void validateAllCodesExist(Set<String> skuCodeSet) {
        List<Sku> skus = findAllById(skuCodeSet);
        if (skus.size() != skuCodeSet.size()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.SKU_NOT_FOUND);
        }
    }
}
