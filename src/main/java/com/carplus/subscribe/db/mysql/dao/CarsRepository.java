package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.GeoDefine;
import com.carplus.subscribe.model.cars.req.CarCriteria;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
public class CarsRepository extends SimpleJpaRepository<Cars, String> {
    private final EntityManager em;

    public CarsRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Cars.class, em);
        this.em = em;
    }

    public void updateIsDeleted(String plateNo, boolean isDeleted) {
        em.createQuery("update Cars c set c.isDeleted = :isDeleted where c.plateNo = :plateNo")
            .setParameter("plateNo", plateNo)
            .setParameter("isDeleted", isDeleted)
            .executeUpdate();
    }

    public Cars findByPlateNo(String plateNo) {
        try {
            return em.createQuery("select c from Cars c where c.plateNo = :plateNo", Cars.class)
                .setParameter("plateNo", plateNo)
                .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public Cars findByCarNo(String carNo) {
        try {
            return em.createQuery("SELECT c FROM Cars c WHERE c.carNo = :carNo", Cars.class)
                .setParameter("carNo", carNo)
                .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public List<Cars> findByPlateNoList(List<String> plateNoList) {
        return em.createQuery("select c from Cars c where c.plateNo in :plateNoList", Cars.class)
            .setParameter("plateNoList", plateNoList)
            .getResultList();
    }

    public List<Cars> findByCrsCarNo(Integer crsCarNo) {
        return em.createQuery("select c from Cars c where c.crsCarNo = :crsCarNo", Cars.class)
            .setParameter("crsCarNo", crsCarNo)
            .getResultList();
    }

    public Cars findByCarModelCode(String carModelCode) {
        try {
            return em.createQuery("select c from Cars c where c.carModelCode = :carModelCode", Cars.class)
                .setParameter("carModelCode", carModelCode)
                .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public int updateLocationStation(@NonNull String plateNo, @Nullable Stations newStation) {
        Cars car = findByPlateNo(plateNo);
        if (car == null) {
            return 0;
        }

        // 檢查車輛狀態是否允許更新位置
        if (CarDefine.CarStatus.Free.getCode().equals(car.getCarStatus())
            || (CarDefine.CarStatus.Subscribed.getCode().equals(car.getCarStatus()) && isStationInSameRegion(car.getLocationStationCode(), newStation))) {
            car.setLocationStationCode(Optional.ofNullable(newStation)
                .map(Stations::getStationCode)
                .orElse(null));
            save(car);
            return 1;
        }
        return 0;
    }

    private boolean isStationInSameRegion(String currentStationCode, Stations newStation) {
        if (currentStationCode == null || newStation == null) {
            return false;
        }
        Stations currentStation = em.find(Stations.class, currentStationCode);
        if (currentStation == null) {
            return false;
        }
        GeoDefine.GeoRegion[] currentGeoRegions = GeoDefine.GeoRegion.stringToEnum(currentStation.getLocateGeoRegion());
        GeoDefine.GeoRegion[] newGeoRegions = GeoDefine.GeoRegion.stringToEnum(newStation.getLocateGeoRegion());
        return Arrays.stream(Objects.requireNonNull(currentGeoRegions))
            .anyMatch(region -> Arrays.asList(Objects.requireNonNull(newGeoRegions)).contains(region));
    }

    public int updateCurrentMileage(String plateNo, int currentMileage) {
        return em.createQuery("update Cars c set c.currentMileage = :currentMileage where c.plateNo = :plateNo")
            .setParameter("plateNo", plateNo)
            .setParameter("currentMileage", currentMileage)
            .executeUpdate();
    }

    /**
     * 更新車輛狀態
     *
     * @param plateNo   車號
     * @param carStatus 狀態
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void lockCar(@NonNull String plateNo, @NonNull CarDefine.CarStatus carStatus, String mainContractNo) {
        String sql = "update cars set carStatus = :status , bookingOrderNo = :mainContractNo where plateNo = :plateNo";
        em.createNativeQuery(sql)
            .setParameter("status", carStatus.getCode())
            .setParameter("mainContractNo", mainContractNo)
            .setParameter("plateNo", plateNo)
            .executeUpdate();
    }


    public CarBrandModelDTO getCarBrandModelByPlateNo(String plateNo) {
        String sql = "select c, cm, cb from Cars c "
            + "left join CarModel cm on cm.carModelCode = c.carModelCode "
            + "left join CarBrand cb on cb.brandCode = cm.brandCode "
            + "where c.plateNo=:plateNo ";

        Query qry = em.createQuery(sql).setParameter("plateNo", plateNo);
        try (Stream<Object[]> obj = qry.getResultStream()) {
            return obj.map(o -> new CarBrandModelDTO((Cars) o[0], (CarModel) o[1], (CarBrand) o[2])).findFirst().orElse(null);
        }
    }

    public long count(@NonNull CarCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, criteria, true);
        return em.createQuery(cq).getResultList().size();
    }

    public List<CarBrandModelDTO> searchQuery(@NonNull CarCriteria criteria, @Nullable Integer limit, @Nullable Integer offset) {
        List<CarBrandModelDTO> result = new ArrayList<>();

        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        prepareQuery(cb, cq, criteria, false);
        TypedQuery<Object[]> query = em.createQuery(cq);
        if (limit != null) {
            query.setMaxResults(limit);
        }
        if (offset != null) {
            query.setFirstResult(offset);
        }
        query.getResultStream().forEach(o -> {
            result.add(new CarBrandModelDTO((Cars) o[0], (CarModel) o[1], (CarBrand) o[2]));
        });
        return result;
    }

    public void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, CarCriteria criteria, boolean isCount) {
        List<Predicate> predicateList = new ArrayList<>();
        Root<Cars> carsRoot = cq.from(Cars.class);
        Join<Cars, CarModel> carModelJoin = carsRoot.join("carModel", JoinType.LEFT);
        Join<CarModel, CarBrand> carBrandJoin = carModelJoin.join("carBrand", JoinType.LEFT);
        Join<Cars, Stations> carsStationsJoin = carsRoot.join("stations", JoinType.LEFT);
        if (criteria.getStationCode() != null && !criteria.getStationCode().isEmpty()) {
            predicateList.add(carsRoot.get(Cars.Fields.locationStationCode).in(criteria.getStationCode()));
        }
        if (criteria.getCarType() != null) {
            predicateList.add(cb.equal(carsRoot.get(Cars.Fields.carType), criteria.getCarType()));
        }
        if (CollectionUtils.isNotEmpty(criteria.getPlateNo())) {
            predicateList.add(carsRoot.get(Cars.Fields.plateNo).in(criteria.getPlateNo()));
        }
        if (criteria.getCarState() != null) {
            predicateList.add(cb.equal(carsRoot.get(Cars.Fields.carState), criteria.getCarState()));
        }
        if (criteria.getCarStatuses() != null && !criteria.getCarStatuses().isEmpty()) {
            predicateList.add(carsRoot.get(Cars.Fields.carStatus).in(criteria.getCarStatuses()));
        }
        if (StringUtils.isNotBlank(criteria.getCarNo())) {
            predicateList.add(cb.equal(carsRoot.get(Cars.Fields.carNo), criteria.getCarNo()));
        }
        if (StringUtils.isNotBlank(criteria.getGeoRegion())) {
            predicateList.add(cb.or(
                carsStationsJoin.get(Stations.Fields.locateGeoRegion).in(GeoDefine.GeoRegion.getNamesBy(criteria.getGeoRegion())),
                carsRoot.get(Cars.Fields.locationStationCode).isNull())
            );
        }
        if (criteria.getBrandCode() != null && !criteria.getBrandCode().isEmpty()) {
            predicateList.add(carBrandJoin.get(CarBrand.Fields.brandCode).in(criteria.getBrandCode()));
        }
        if (criteria.getCarModelCode() != null && !criteria.getCarModelCode().isEmpty()) {
            predicateList.add(carModelJoin.get(CarModel.Fields.carModelCode).in(criteria.getCarModelCode()));
        }
        if (criteria.getSubscribeLevel() != null && !criteria.getSubscribeLevel().isEmpty()) {
            predicateList.add(carsRoot.get(Cars.Fields.subscribeLevel).in(criteria.getSubscribeLevel()));
        }
        if (criteria.getLaunched() != null && !criteria.getLaunched().isEmpty()) {
            predicateList.add(carsRoot.get(Cars.Fields.launched).in(criteria.getLaunched()));
        }
        if (criteria.getIsDelete() != null) {
            predicateList.add(cb.equal(carsRoot.get(Cars.Fields.isDeleted), criteria.getIsDelete()));
        }
        if (criteria.getTagIds() != null && !criteria.getTagIds().isEmpty()) {
            predicateList.add(cb.greaterThan(cb.function("JSON_OVERLAPS", Integer.class, carsRoot.get(Cars.Fields.tagIds), cb.literal("[" + criteria.getTagIds().stream().map(Objects::toString).collect(Collectors.joining(",")) + "]")), 0));
        }
        if (criteria.getIsFilterVirtualCar() != null && criteria.getIsFilterVirtualCar()) {
            predicateList.add(cb.or(
                cb.notLike(carsRoot.get(Cars.Fields.plateNo), "RAA%"),
                cb.like(carsRoot.get(Cars.Fields.plateNo), "RAA-%")
            ));
        }
        if (criteria.getIsSealandLaunched() != null) {
            predicateList.add(cb.equal(carsRoot.get(Cars.Fields.isSealandLaunched), criteria.getIsSealandLaunched()));
        }
        if (CollectionUtils.isNotEmpty(criteria.getVatNo())) {
            predicateList.add(carsRoot.get(Cars.Fields.vatNo).in(criteria.getVatNo()));
        }
        if (CollectionUtils.isNotEmpty(criteria.getEnergyType())) {
            predicateList.add(carsRoot.get(Cars.Fields.energyType).in(criteria.getEnergyType()));
        }

        if (isCount) {
            cq.select(carsRoot.get(Cars.Fields.plateNo));
        } else {
            cq.multiselect(carsRoot, carModelJoin, carBrandJoin);
        }
        cq.where(predicateList.toArray(new Predicate[0]));
    }

    public Map<String, Integer> countByStationCode(List<String> stationCodeList) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        Root<Cars> root = cq.from(Cars.class);

        cq.multiselect(
            root.get(Cars.Fields.locationStationCode),
            cb.count(root)
        );

        cq.where(cb.and(root.get(Cars.Fields.locationStationCode).in(stationCodeList), cb.equal(root.get(Cars.Fields.isDeleted), false)));
        cq.groupBy(root.get(Cars.Fields.locationStationCode));

        List<Object[]> results = em.createQuery(cq).getResultList();

        return results.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],
                row -> ((Long) row[1]).intValue()
            ));
    }


    public List<Cars> getIdleCar() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Cars> cq = cb.createQuery(Cars.class);
        Root<Cars> root = cq.from(Cars.class);
        Predicate predicate = cb.and(
            cb.equal(root.get(Cars.Fields.carStatus), CarDefine.CarStatus.Free.getCode()),
            cb.notEqual(root.get(Cars.Fields.launched), CarDefine.Launched.deprecate),
            cb.notEqual(root.get(Cars.Fields.launched), CarDefine.Launched.tbc),
            cb.isNull(root.get(Cars.Fields.buChangeMasterId)),
            cb.equal(root.get(Cars.Fields.vatNo), CarPlusConstant.CARPLUS_COMPANY_VAT_NO),
            cb.equal(root.get(Cars.Fields.isDeleted), false),
            cb.or(
                cb.notLike(root.get(Cars.Fields.plateNo), "RAA%"),
                cb.like(root.get(Cars.Fields.plateNo), "RAA-%")
            )

        );
        cq.where(predicate);
        return em.createQuery(cq).getResultList();
    }

    public void refresh(Cars cars) {
        em.refresh(cars);
    }
}
