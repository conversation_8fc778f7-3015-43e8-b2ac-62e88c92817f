package com.carplus.subscribe.controller.priceinfo;

import carplus.common.model.Page;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.model.etag.ETagInfoRequest;
import com.carplus.subscribe.model.priceinfo.req.InternalAccidentRequest;
import com.carplus.subscribe.model.priceinfo.req.InternalUpdateMileagePriceInfoRequest;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoWithDescription;
import com.carplus.subscribe.model.request.priceinfo.ExtraFeeRequest;
import com.carplus.subscribe.model.request.priceinfo.MerchandiseInfoRequest;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoCriteriaRequest;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoUpdateRequest;
import com.carplus.subscribe.service.ETagService;
import com.carplus.subscribe.service.OrderService;
import com.carplus.subscribe.service.PriceInfoService;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.annotation.TrackPriceInfoUpdator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 訂單費用資訊API")
public class InternalPriceInfoController {

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ETagService etagService;

    @Operation(summary = "檢視明細")
    @GetMapping(value = "subscribe/priceInfo/{priceInfoId}/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderPriceInfo getInfo(@PathVariable("priceInfoId") Integer priceInfoId) {
        OrderPriceInfo info = priceInfoService.get(priceInfoId);
        OrderPriceInfo resp = new OrderPriceInfo();
        BeanUtils.copyProperties(info, resp, "order");
        return resp;
    }

    @Operation(summary = "訂單付款資訊清單")
    @GetMapping(value = "/subscribe/priceInfo/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> priceInfo(
        @PathVariable("orderNo") String orderNo, OrderPriceInfoCriteriaRequest request) {
        return priceInfoService.getPriceInfosByOrder(orderNo, request);
    }

    @Operation(summary = "付款資訊清單")
    @GetMapping(value = "/subscribe/priceInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<OrderPriceInfoWithDescription> priceInfo(OrderPriceInfoCriteriaRequest request) {
        request.validateForWeb();
        return priceInfoService.searchPageable(request);
    }

    @Operation(summary = "付款資訊清單 csv")
    @GetMapping(value = "/subscribe/priceInfo/csv/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] priceInfoCsvExport(HttpServletResponse res, OrderPriceInfoCriteriaRequest request) {
        request.validateForWeb();
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = priceInfoService.generateCsv(request);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=priceInfo_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "設定費用時間")
    @PatchMapping(value = "/subscribe/v1/priceInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updatePriceInfo(
        @RequestBody @Validated OrderPriceInfoUpdateRequest updateRequest) {
        priceInfoService.updateOrderPriceInfo(updateRequest);
    }

    @Operation(summary = "設定額外費用")
    @TrackPriceInfoUpdator(description = "設定額外費用")
    @PostMapping(value = "/subscribe/priceInfo/{orderNo}/extraFee", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> setExtraFee(@PathVariable("orderNo") String orderNo,
                                            @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                            @RequestBody ExtraFeeRequest extraFeeRequest) {
        priceInfoService.setExtraFee(orderNo, extraFeeRequest, memberId, false);
        return priceInfoService.getFeePriceInfos(orderNo);
    }

    @Operation(summary = "設定汽車用品費用")
    @TrackPriceInfoUpdator(description = "設定汽車用品費用")
    @PostMapping(value = "/subscribe/priceInfo/{orderNo}/merchandise", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> setMerchandiseFee(@PathVariable("orderNo") String orderNo,
                                                  @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                  @RequestBody @Valid MerchandiseInfoRequest merchandiseInfoRequest) {
        priceInfoService.setMerchandiseFee(orderNo, merchandiseInfoRequest.getMerchandiseList(), memberId);
        return priceInfoService.getPriceInfosByOrder(orderNo);
    }

    @Operation(summary = "使用者訂單需付款資訊清單")
    @GetMapping(value = "/subscribe/priceInfo/{orderNo}/unpaid", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfoResponse> orderPriceInfo(
        @PathVariable("orderNo") String orderNo,
        @RequestParam(value = "history", defaultValue = "false", required = false) Boolean history,
        @RequestParam(value = "isCredit", defaultValue = "true", required = false) Boolean isCredit) {
        return priceInfoService.getUnPaidPriceInfoByOrderResponse(orderNo, history, isCredit);
    }

    @Operation(summary = "設定里程費")
    @TrackPriceInfoUpdator(description = "設定里程費")
    @PatchMapping(value = "/subscribe/v1/priceInfo/{orderNo}/mileageAmt", produces = MediaType.APPLICATION_JSON_VALUE)
    public OrderPriceInfo setMileage(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @RequestBody @Validated InternalUpdateMileagePriceInfoRequest updateMileagePriceInfoRequest,
        @PathVariable("orderNo") String orderNo) {
        priceInfoService.calculateMillageFeeValidate(orderNo, updateMileagePriceInfoRequest.getAcctId(), updateMileagePriceInfoRequest.getOrderPriceInfoId());
        return priceInfoService.calculateMillageFee(orderNo, updateMileagePriceInfoRequest.getAcctId(), updateMileagePriceInfoRequest.getCurrentMileage(), updateMileagePriceInfoRequest.getOrderPriceInfoId());
    }

    @Operation(summary = "設定車損費用")
    @TrackPriceInfoUpdator(description = "設定車損費用")
    @PostMapping(value = "/subscribe/v1/priceInfo/{orderNo}/accident", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> setAccidentPriceInfo(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                     @RequestBody ExtraFeeRequest extraFeeRequest,
                                                     @PathVariable("orderNo") String orderNo) {
        priceInfoService.setExtraFee(orderNo, extraFeeRequest, memberId, true);
        return priceInfoService.getFeePriceInfos(orderNo);
    }

    @Operation(summary = "設定車損費用 by Contract")
    @TrackPriceInfoUpdator(description = "設定車損費用 by Contract")
    @PostMapping(value = "/subscribe/v1/priceInfo/contract/{contractNo}/accident", produces = MediaType.APPLICATION_JSON_VALUE)
    public void setAccidentPriceInfoByContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                               @RequestBody InternalAccidentRequest accidentRequest,
                                               @PathVariable("contractNo") String contractNo) {
        orderService.setAccidentInfoByContract(memberId, contractNo, accidentRequest);
    }

    @Operation(summary = "使用者取消訂單款項資訊清單")
    @GetMapping(value = "/subscribe/priceInfo/{orderNo}/cancel", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfoResponse> cancelOrderPriceInfo(
        @PathVariable("orderNo") String orderNo) {
        return priceInfoService.getCancelPriceInfo(orderNo);
    }

    @Operation(summary = "人工設定Etag費用")
    @TrackPriceInfoUpdator(description = "人工設定Etag費用")
    @PatchMapping(value = "/subscribe/priceInfo/{orderNo}/etagAmt", produces = MediaType.APPLICATION_JSON_VALUE)
    public void setEtagAmt(@PathVariable("orderNo") String orderNo,
                           @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                           @RequestBody @Validated ETagInfoRequest etagInfoRequest) {
        etagService.manualSetEtagAmt(orderNo, memberId, etagInfoRequest);
    }
}
