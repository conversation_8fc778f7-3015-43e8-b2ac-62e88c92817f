package com.carplus.subscribe.controller.sku;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.request.sku.SkuAddRequest;
import com.carplus.subscribe.model.request.sku.SkuCriteria;
import com.carplus.subscribe.model.request.sku.SkuUpdateRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.sku.SkuResponse;
import com.carplus.subscribe.service.SkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 周邊商品API")
public class SkuInternalController {

    @Autowired
    private SkuService skuService;

    @Operation(summary = "新增周邊商品")
    @PostMapping(value = "/subscribe/sku", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addSku(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                       @RequestBody @Validated SkuAddRequest skuAddRequest) {
        skuService.addSku(skuAddRequest);
    }

    @Operation(summary = "取得周邊商品")
    @GetMapping(value = "/subscribe/sku/{code}", produces = MediaType.APPLICATION_JSON_VALUE)
    public SkuResponse getSku(@PathVariable String code) {
        return skuService.getByCode(code);
    }

    @Operation(summary = "取得所有周邊商品分頁列表")
    @GetMapping(value = "/subscribe/sku", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<SkuResponse> getSkus(@Validated SkuCriteria criteria) {
        criteria.setIsCashier(true);
        return PageResponse.of(skuService.searchByPage(new PageRequest(criteria.getLimit(), criteria.getSkip()), criteria, SkuResponse::new));
    }

    @Operation(summary = "更新周邊商品")
    @PatchMapping(value = "/subscribe/sku/{code}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateSku(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                          @PathVariable String code, @RequestBody @Validated SkuUpdateRequest skuUpdateRequest) {
        skuService.updateSku(code, skuUpdateRequest);
    }
}
