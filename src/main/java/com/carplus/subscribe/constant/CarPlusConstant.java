package com.carplus.subscribe.constant;

import java.util.Arrays;
import java.util.List;

public class CarPlusConstant {
    public static final String AUTH_HEADER_PLATFORM = "X-Platform";
    public static final String AUTH_HEADER_ACCT = "X-AcctId";
    public static final String AUTH_HEADER_MEMBER = "X-MemberId";
    public static final String AUTH_HEADER_SYSTEM_KIND = "X-System-Kind";
    public static final String AUTH_HEADER_VERSION = "X-Version";
    public static final String AUTH_HEADER_TID = "X-Tid";
    public static final String AUTH_HEADER_LID = "X-Lid";


    public static final String REMOTE_IP = "remoteIp";
    public static final String HEADER_COMPANY_CODE = "X-CompanyId";
    public static final String CARPLUS_COMPANY_CODE = "carplus";
    public static final String PLATE_FORM_CODE = "Server";

    public static final String ERROR_MSG = "errorMsg";

    public static final String SUBSCRIBE_MANAGEMENT_DEPT_CODE = "B11001";
    public static final String departmentName = "訂閱課";
    public static final String SUBSCRIBE_BUSINESS_DEPT_CODE = "B11002";
    public static final List<String> SUBSCRIBE_DEPT_CODES = Arrays.asList(SUBSCRIBE_MANAGEMENT_DEPT_CODE, SUBSCRIBE_BUSINESS_DEPT_CODE);
    public static final String departmentMasterCode = "B17100";
    public static final String departmentMasterName = "訂閱暨中古車營業部";
    public static final String SUB_VIRTUAL_VAT_NO = "Y99999SU";

    public static final String SEALAND_VIRTUAL_PLATE_NO = "RAA9999";

    public static final String ECONTRACT_CODE = "ECON";


    public static final String TIME_ZONE = "Asia/Taipei";

    public static final String CARPLUS_SUBSCRIBE_CAR = "【格上訂閱車】";
    public static final String CARPLUS_SUBSCRIBE_CAR_TEST = "【測試】";
    public static final String SHORT_URL = "/s/SUBORDER";

    public static final String CARPLUS_COMPANY_VAT_NO = "12208883";

    public static final String CAR_WISHLIST_REDIS_KEY = "subscribe:car-wishlist:";

    public static final int CAR_WISHLIST_LIMIT_PER_USER = 200;

}
