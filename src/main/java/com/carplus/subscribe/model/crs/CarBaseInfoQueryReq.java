package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "車輛基本資料查詢")
public class CarBaseInfoQueryReq {

//    /**
//     * 廠牌代碼
//     */
//    @Schema(description = "廠牌代碼")
//    private String brandCode;
//
//    /**
//     * 庫位List
//     */
//    @Schema(description = "庫位List", example = "[2]")
//    private List<Integer> buList;
//
//    /**
//     * 營運狀態List
//     */
//    @Schema(description = "營運狀態List")
//    private List<String> buStatusList;
//
//    /**
//     * 車型代碼列表(8 or 9碼)
//     */
//    @Schema(description = "車型代碼列表(8 or 9碼)")
//    private List<String> carCodeList;

    /**
     * 車輛編號List
     */
    @Schema(description = "車輛編號List")
    private List<Integer> carNoList;

//    /**
//     * carbaseUpdateDateEnd
//     */
//    @Schema(description = "carbaseUpdateDateEnd")
//    private Instant carbaseUpdateDateEnd;
//
//    /**
//     * carbaseUpdateDateStart
//     */
//    @Schema(description = "carbaseUpdateDateStart")
//    private Instant carbaseUpdateDateStart;
//
//    /**
//     * 能源別List
//     */
//    @Schema(description = "能源別List")
//    private List<Integer> carplusEnergyCodeList;
//
//    /**
//     * 車型代碼
//     */
//    @Schema(description = "車型代碼")
//    private String classCode;
//
//    /**
//     * 排氣量-迄
//     */
//    @Schema(description = "排氣量-迄")
//    private Integer cylinderEnd;
//
//    /**
//     * 排氣量-起
//     */
//    @Schema(description = "排氣量-起")
//    private Integer cylinderStart;
//
//    /**
//     * 是否計算已開放天數(預設：false)
//     */
//    @Schema(description = "是否計算已開放天數(預設：false)")
//    private Boolean isCountOpenDay;
//
//    /**
//     * 限制user只可查詢所屬庫位資料(空值則不限制)
//     */
//    @Schema(description = "限制user只可查詢所屬庫位資料(空值則不限制)")
//    private String isLimitBUofUser;
//
//    /**
//     * 是否開放撥車
//     */
//    @Schema(description = "是否開放撥車")
//    private Boolean isOpen;
//
//    /**
//     * 是否需要查詢車型資訊(預設：true)
//     */
//    @Schema(description = "是否需要查詢車型資訊(預設：true)")
//    private Boolean isQueryCarCode;
//
//    /**
//     * 是否需要查詢車籍公司資訊(預設：false)
//     */
//    @Schema(description = "是否需要查詢車籍公司資訊(預設：false)")
//    private Boolean isQueryCarplusCompany;
//
//    /**
//     * 是否查詢專案車(預設：false)
//     */
//    @Schema(description = "是否查詢專案車(預設：false)")
//    private Boolean isQueryProjectCar;
//
//    /**
//     * 是否查詢舊車籍會計車輛資產資訊(預設：false)
//     */
//    @Schema(description = "是否查詢舊車籍會計車輛資產資訊(預設：false)")
//    private Boolean isQueryProperty;
//
//    /**
//     * 是否需要向子系統取得車輛資訊(預設：true)
//     */
//    @Schema(description = "是否需要向子系統取得車輛資訊(預設：true)")
//    private Boolean isQuerySubSystem;
//
//    /**
//     * 里程數-迄
//     */
//    @Schema(description = "里程數-迄")
//    private Integer kmEnd;
//
//    /**
//     * 里程數-起
//     */
//    @Schema(description = "里程數-起")
//    private Integer kmStart;
//
//    /**
//     * licenseStatusChangeDateEnd
//     */
//    @Schema(description = "licenseStatusChangeDateEnd")
//    private Instant licenseStatusChangeDateEnd;
//
//    /**
//     * licenseStatusChangeDateStart
//     */
//    @Schema(description = "licenseStatusChangeDateStart")
//    private Instant licenseStatusChangeDateStart;
//
//    /**
//     * 車牌狀態代碼List
//     */
//    @Schema(description = "車牌狀態代碼List")
//    private List<String> licenseStatusList;
//
//    /**
//     * licenseUpdateDateEnd
//     */
//    @Schema(description = "licenseUpdateDateEnd")
//    private Instant licenseUpdateDateEnd;
//
//    /**
//     * licenseUpdateDateStart
//     */
//    @Schema(description = "licenseUpdateDateStart")
//    private Instant licenseUpdateDateStart;

    /**
     * 車牌號碼List
     */
    @Schema(description = "車牌號碼List")
    private List<String> plateNoList;

//    /**
//     * 出廠年份-迄
//     */
//    @Schema(description = "出廠年份-迄", example = "2020/01")
//    private String publishDateEnd;
//
//    /**
//     * 出廠年份-起
//     */
//    @Schema(description = "出廠年份-起", example = "2020/01")
//    private String publishDateStart;
//
//    /**
//     * 座位數-迄
//     */
//    @Schema(description = "座位數-迄", example = "5")
//    private Integer seatsEnd;
//
//    /**
//     * 座位數-起
//     */
//    @Schema(description = "座位數-起", example = "2")
//    private Integer seatsStart;
//
//    /**
//     * updateStatusDateEnd
//     */
//    @Schema(description = "updateStatusDateEnd")
//    private Instant updateStatusDateEnd;
//
//    /**
//     * updateStatusDateStart
//     */
//    @Schema(description = "updateStatusDateStart")
//    private Instant updateStatusDateStart;

    /**
     * 取幾筆
     */
    @Schema(description = "取幾筆")
    private int limit = 100;

    /**
     * 從第幾筆開始(預設：0)
     */
    @Schema(description = "從第幾筆開始(預設：0)")
    private int offset;

//    @Schema(description = "車輛編號")
//    private Integer carNo;
//
//    @Schema(description = "公司序號")
//    private String companyNo;
//
//    @Schema(description = "引擎編號")
//    private String engineNo;
//
//    @Schema(description = "引擎編號List")
//    private List<String> engineNoList;
//
//    @Schema(description = "車身號碼")
//    private String bodyNo;
//
//    @Schema(description = "標準售價代碼")
//    private String stdPriceCode;
//
//    @Schema(description = "車型代碼4")
//    private String code4;
//
//    @Schema(description = "購買類型")
//    private String carSource;
//
//    @Schema(description = "車牌號碼")
//    private String plateNo;
//
//    @Schema(description = "庫位代碼")
//    private Integer buId;
//
//    @Schema(description = "營運狀態 service_code=car_base-buStatus")
//    private String buStatus;
//
//    @Schema(description = "資產編號")
//    private Integer pyAuto;
//
//    @Schema(description = "異動日期-起")
//    private Timestamp updateDateStart;
//
//    @Schema(description = "異動日期-訖")
//    private Timestamp updateDateEnd;
}

