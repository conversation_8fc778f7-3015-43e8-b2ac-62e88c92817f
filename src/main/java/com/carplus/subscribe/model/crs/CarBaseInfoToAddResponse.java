package com.carplus.subscribe.model.crs;

import com.carplus.subscribe.enums.CarDefine;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Data
@Schema(description = "查詢車輛資訊結果，用於新增車籍")
public class CarBaseInfoToAddResponse {

    @Schema(description = "能源類別")
    @Enumerated(EnumType.STRING)
    private CarDefine.EnergyType energyType;

    @Schema(description = "排氣量")
    private BigDecimal displacement;

    @Schema(description = "座位數")
    private Integer seat;

    @Schema(description = "出廠年份")
    private String mfgYear;

    @Schema(description = "出廠月份")
    private String mfgMonth;

    @Schema(description = "里程數")
    private Integer currentMileage;

    @Schema(description = "當前所在站所代碼")
    private String locationStationCode;

    @Schema(description = "車型編號")
    private String carModelCode;

    @Schema(description = "廠牌代碼(參考用，不會影響車籍單台新增結果)")
    private String brandCode;

    @Schema(description = "訂閱類別, NEW:新車; OLD:中古車", example = "OLD")
    private CarDefine.CarState carState;

    @Schema(description = "訂閱車方案")
    private Integer subscribeLevel;

    @Schema(description = "上下架狀態 (是否在訂閱官網上架)")
    private CarDefine.Launched launched;

    @Schema(description = "是否開放經銷商上架")
    private Boolean isSealandLaunched;

    @Schema(description = "標籤")
    private List<Integer> tagIds;

    @Schema(description = "配備")
    private List<Integer> equipIds;

    @Schema(description = "排檔類別")
    private CarDefine.GearType gearType;

    @Schema(description = "車色")
    private String colorDesc;

    @Schema(description = "車體介紹")
    private String cnDesc;

    @Schema(description = "燃料種類")
    private CarDefine.FuelType fuelType;

    @Schema(description = "車型類別")
    private CarDefine.CarType carType;

    @JsonIgnore
    private Integer crsCarNo;

    public CarBaseInfoToAddResponse(CarBaseInfoSearchResponse carBaseInfoSearchResponse) {
        this.energyType = CarDefine.EnergyType.of(carBaseInfoSearchResponse.getCarSpecInfoResponse().getCarPlusEnergyCode());
        this.displacement = BigDecimal.valueOf(carBaseInfoSearchResponse.getCarSpecInfoResponse().getCylinder());
        this.seat = carBaseInfoSearchResponse.getCarSpecInfoResponse().getSeats();
        Optional.ofNullable(carBaseInfoSearchResponse.getCarBase().getPublishDate()).ifPresent(publishDate -> {
            publishDate = publishDate.trim();
            if (publishDate.length() >= 7) {
                this.mfgYear = publishDate.substring(0, 4);
                this.mfgMonth = publishDate.substring(5, 7);
            }
        });
        this.currentMileage = carBaseInfoSearchResponse.getCarBase().getKm();
        Optional.ofNullable(carBaseInfoSearchResponse.getSubSystemCarInfo())
            .ifPresent(subSystemCarInfo -> this.setLocationStationCode(subSystemCarInfo.getLocationId()));
        this.crsCarNo = carBaseInfoSearchResponse.getCarNo();
    }
}
