package com.carplus.subscribe.model.priceinfo.resp;

import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.text.NumberFormat;
import java.time.Instant;
import java.util.*;

@Data
@NoArgsConstructor
@Slf4j
public class OrderPriceInfoResponse implements PriceInfoInterface {

    @Schema(description = "款項編號")
    private Integer id;

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "期數")
    private Integer stage;

    @Schema(description = "最後付款期限")
    private Instant lastPayDate;

    @Schema(description = "可開始收款日期")
    private Instant receivableDate;

    @Schema(description = "費用類別")
    private PriceInfoDefinition.PriceInfoCategory category;

    @Schema(description = "保證金資訊")
    private SecurityDepositInfo securityDepositInfo;

    @Schema(description = "關聯費用資訊")
    private List<RelatedPriceInfo> relatedPriceInfos;

    @Schema(description = "款項細部內容")
    private PriceInfoDetail infoDetail;

    @Schema(description = "匯款登打ID")
    private List<Long> remitAccountIds;

    @Schema(description = "費用型態")
    private int type;

    @Schema(description = "費用")
    private Integer amount = 0;

    @Schema(description = "已收費用")
    private Integer receivedAmount = 0;

    @Schema(description = "recTradeId編號")
    private String recTradeId;

    @Schema(description = "refundId 退款編號")
    private String refundId;

    @Schema(description = "payment編號")
    private Integer paymentId;

    @Schema(description = "關聯費用資訊編號")
    private Integer refPriceInfoNo;

    @Schema(description = "群組編號，審核用")
    private String uid;

    @Schema(description = "類別名稱")
    private String categoryName;

    @Schema(description = "付款狀態")
    private PayStatus payStatus;

    @Schema(description = "付款狀態名稱")
    private String payStatusName;

    @Schema(description = "公式名稱")
    private String formulaDescription;

    @Schema(description = "說明")
    private String memo;

    @Schema(description = "etag資訊")
    private ETagInfo eTagInfo;

    @Schema(description = "期數資訊")
    private CalculateStage calculateStage;

    @Schema(description = "加購周邊商品代碼")
    private String skuCode;

    @Schema(description = "最後更新人員編號")
    private String updator;

    @Builder
    public static class SecurityDepositInfo {
        @Schema(description = "保證金金額")
        private Integer amount;

        @Schema(description = "付款日期")
        private Instant paidDate;

        @Schema(description = "系統退款日期")
        private Instant refundDate;

        @Schema(description = "人工退款日期")
        private Instant manualRefundDate;
    }

    public OrderPriceInfoResponse(OrderPriceInfo orderPriceInfo) {
        initializeCommonFields(orderPriceInfo);
        setPaidStatus();
        prepareFormulaDescription(null);
    }

    public OrderPriceInfoResponse(OrderPriceInfo orderPriceInfo, Map<String, Sku> skuMap) {
        initializeCommonFields(orderPriceInfo);
        setPaidStatus();
        prepareFormulaDescription(skuMap);
    }

    public OrderPriceInfoResponse(OrderPriceInfo orderPriceInfo, ETagInfo etagInfo, CalculateStage calculateStage, Map<String, Sku> skuMap) {
        initializeCommonFields(orderPriceInfo);
        this.calculateStage = calculateStage;
        setPaidStatus();
        prepareFormulaDescription(skuMap);
        prepareETagInfoMemo(etagInfo);
    }

    private void initializeCommonFields(OrderPriceInfo orderPriceInfo) {
        copyFieldsFromOrderPriceInfo(orderPriceInfo);
        this.categoryName = orderPriceInfo.getCategory().getDescriptionName();
    }

    private void copyFieldsFromOrderPriceInfo(OrderPriceInfo orderPriceInfo) {
        this.id = orderPriceInfo.getId();
        this.orderNo = orderPriceInfo.getOrderNo();
        this.stage = orderPriceInfo.getStage();
        this.lastPayDate = orderPriceInfo.getLastPayDate();
        this.receivableDate = orderPriceInfo.getReceivableDate();
        this.category = orderPriceInfo.getCategory();
        this.infoDetail = orderPriceInfo.getInfoDetail();
        this.remitAccountIds = orderPriceInfo.getRemitAccountIds();
        this.type = orderPriceInfo.getType();
        this.amount = orderPriceInfo.getAmount();
        this.receivedAmount = orderPriceInfo.getReceivedAmount();
        this.recTradeId = orderPriceInfo.getRecTradeId();
        this.refundId = orderPriceInfo.getRefundId();
        this.paymentId = orderPriceInfo.getPaymentId();
        this.refPriceInfoNo = orderPriceInfo.getRefPriceInfoNo();
        this.uid = orderPriceInfo.getUid();
        this.skuCode = orderPriceInfo.getSkuCode();
        this.updator = orderPriceInfo.getUpdator();
    }

    private void setPaidStatus() {
        if (StringUtils.isNotBlank(uid)) {
            Boolean isAgree = Optional.ofNullable(infoDetail).map(PriceInfoDetail::getIsAgree).orElse(null);
            if (isAgree == null) {
                this.payStatus = PayStatus.CREDIT;
                this.payStatusName = payStatus.getDisplayName(type);
            } else if (!isAgree) {
                this.payStatus = PayStatus.NONE_PASS;
                this.payStatusName = payStatus.getDisplayName(type);
            }
            if (!Objects.equals(Boolean.TRUE, isAgree)) {
                return;
            }
        }
        if (isPaid()) {
            this.payStatus = PayStatus.PAID;
        } else if (receivedAmount == 0 && receivableDate.isBefore(Instant.now())) {
            this.payStatus = PayStatus.UNPAID;
        } else {
            this.payStatus = PayStatus.PENDING;
        }
        this.payStatusName = payStatus.getDisplayName(type);
    }

    private void prepareFormulaDescription(Map<String, Sku> skuMap) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        try {
            switch (category) {
                case MonthlyFee:
                    if (type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        formulaDescription = String.format("($%sx%d)", numberFormat.format(infoDetail.getMonthlyFee()), infoDetail.getMonth());
                        memo = String.format("月費費率($%s) x 預收月數(%d月)", numberFormat.format(infoDetail.getMonthlyFee()), infoDetail.getMonth());
                    }
                    break;
                case Insurance:
                    if (type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        int amountPerMonth = amount / infoDetail.getMonth();
                        formulaDescription = String.format("($%sx%d)", numberFormat.format(Optional.ofNullable(infoDetail.getInsurance()).orElse(amountPerMonth)), Optional.ofNullable(infoDetail.getMonth()).orElse(0));
                        memo = String.format("每月保障費率($%s) x 預收月數(%d月)", numberFormat.format(Optional.ofNullable(infoDetail.getInsurance()).orElse(amountPerMonth)), Optional.ofNullable(infoDetail.getMonth()).orElse(0));
                    }
                    break;
                case Replacement:
                    if (type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        int amountPerMonth = amount / infoDetail.getMonth();
                        formulaDescription = String.format("($%sx%d)", numberFormat.format(Optional.ofNullable(infoDetail.getReplacementCarFee()).orElse(amountPerMonth)), Optional.ofNullable(infoDetail.getMonth()).orElse(0));
                        memo = String.format("每月代步車($%s) x 預收月數(%d月)", numberFormat.format(Optional.ofNullable(infoDetail.getReplacementCarFee()).orElse(amountPerMonth)), Optional.ofNullable(infoDetail.getMonth()).orElse(0));
                    }
                    break;
                case MileageFee:
                    if (type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        //  [A 為行駛里程、B 為優惠里程]
                        //
                        //1.    A為空 → $3x行駛里程
                        //
                        //2.    A、B皆不為0
                        //         A ≧ B → $3x(A-B)
                        //         A < B → $3x(A-A)
                        //
                        //3.    A為0、不論B是否為0→ $3xA
                        //
                        //4.    A不為0、B為0 → $3xA
                        formulaDescription = String.format("($%.2fx%s)", infoDetail.getMileageFee(), Optional.ofNullable(infoDetail.getTotalMileage()).map(info -> {
                            String description = "(%s-%s)";
                            Integer useMileageFee = Optional.ofNullable(infoDetail.getEndMileage()).orElse(0) - Optional.ofNullable(infoDetail.getStartMileage()).orElse(0);
                            Integer discountMileageFee = Optional.ofNullable(infoDetail.getDiscountMileage()).orElse(0);
                            if (useMileageFee > 0 && discountMileageFee > 0) {
                                if (useMileageFee >= discountMileageFee) {
                                    description = String.format(description, numberFormat.format(useMileageFee), numberFormat.format(discountMileageFee));
                                } else {
                                    description = String.format(description, numberFormat.format(useMileageFee), numberFormat.format(useMileageFee));
                                }
                            } else {
                                description = numberFormat.format(useMileageFee);
                            }
                            return description;
                        }).orElse("行駛里程"));
                        memo = String.format("里程費率($%.2f) x (行駛里程(%skm-%skm) - 折抵里程(%skm))", infoDetail.getMileageFee(),
                            numberFormat.format(Optional.ofNullable(infoDetail.getEndMileage()).orElse(0)),
                            numberFormat.format(Optional.ofNullable(infoDetail.getStartMileage()).orElse(0)),
                            numberFormat.format(Optional.ofNullable(infoDetail.getDiscountMileage()).orElse(0)));
                    }
                    break;
                case PayLate:
                case ReturnLate:
                    if (type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        String fine = numberFormat.format(infoDetail.getMonthlyFee());
                        Integer delayDays = Optional.ofNullable(infoDetail.getDelayDays()).orElse(infoDetail.getDay());
                        Integer insurance = Optional.ofNullable(infoDetail.getInsurance()).orElse(0);
                        if (insurance != 0) {
                            fine = String.format("(%s+%s)", fine, numberFormat.format(infoDetail.getInsurance()));
                        }
                        formulaDescription = String.format("($%sx0.2x%d)", fine, delayDays);
                        memo = insurance != 0 ? String.format("(月費費率($%s) + 每月保障費率($%s)) x 0.2 x 逾期天數(%d天)", fine, numberFormat.format(insurance), delayDays)
                            : String.format("月費費率($%s) x 0.2 x 逾期天數(%d天)", fine, delayDays);
                    }
                    break;
                case CancelBooking:
                    if (type != PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        formulaDescription = String.format("($%sx%s%s)", numberFormat.format(infoDetail.getOriginAmount()), numberFormat.format(100L * infoDetail.getDiscount() / infoDetail.getOriginAmount()), "%");
                    }
                    break;
                case Merchandise:
                    if (type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
                        formulaDescription = String.format("($%sx%d)", numberFormat.format(Optional.ofNullable(infoDetail.getActualUnitPrice()).orElse(infoDetail.getUnitPrice())), infoDetail.getQuantity());
                        if (StringUtils.isNotBlank(skuCode) && skuMap != null && skuMap.containsKey(skuCode)) {
                            memo = skuMap.get(skuCode).getName();
                        } else {
                            memo = categoryName;
                        }
                    }
                    break;
                case Others:
                    formulaDescription = Optional.ofNullable(infoDetail).map(PriceInfoDetail::getReason).orElse(null);
                    break;
                case Dispatch:
                case CarAccident:
                    break;
                default:
                    memo = categoryName;
            }
        } catch (Exception e) {
            // ignore
            log.error("描述失敗", e);
        }
    }

    private void prepareETagInfoMemo(ETagInfo etagInfo) {
        if (etagInfo != null && category.equals(PriceInfoDefinition.PriceInfoCategory.ETag) && type == PriceInfoDefinition.PriceInfoType.Pay.getCode()) {
            String etagFlowName;
            String msg;
            switch (etagInfo.getETagFlow()) {
                case 21:
                    etagFlowName = "DEPART_SUCCESS";
                    msg = "租車成功";
                    break;
                case 22:
                    etagFlowName = "DEPART_ETAG_FAIL";
                    msg = "租車失敗 (有其他遠通契約未結案)";
                    break;
                case 23:
                    etagFlowName = "DEPART_NOT_VALID";
                    msg = "租車失敗 (尚未加入格上車隊)";
                    break;
                case 61:
                    etagFlowName = "RETURN_SUCCESS";
                    msg = "還車成功";
                    break;
                case 62:
                    etagFlowName = "RETURN_FAIL";
                    msg = "還車失敗";
                    break;
                case 64:
                    etagFlowName = "RETURN_OTHER";
                    msg = "租車失敗導致還車失敗";
                    break;
                default:
                    etagFlowName = "未知出還車流程節點";
                    msg = "無法識別的處理狀態";
                    break;
            }
            this.eTagInfo = etagInfo;
            memo = String.format("遠通契約區間 %s ~ %s ( 狀態：%s；%s )",
                DateUtils.toDateString(Date.from(etagInfo.getDepartDate()), "yyyy/MM/dd HH:mm"),
                DateUtils.toDateString(Date.from(etagInfo.getReturnDate()), "yyyy/MM/dd HH:mm"),
                etagFlowName, msg);
        }
    }
}
