package com.carplus.subscribe.model.priceinfo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Optional;

@Data
public class PriceInfoDetail {
    /**
     * 起始里程
     */
    private Integer startMileage;
    /**
     * 結束里程
     */
    private Integer endMileage;
    /**
     * 優惠里程數
     */
    private Integer discountMileage;
    /**
     * 原始里程優惠數
     */
    private Integer originalDiscountMileage;
    /**
     * 續約里程
     */
    private Integer renewDiscountMileage;
    /**
     * 租期里程
     */
    private Integer rentalDiscountMileage;
    /**
     * 總里程
     */
    private Integer totalMileage;

    /**
     * 每公里里程費
     */
    private Double mileageFee;

    /**
     * 月費
     */
    private Integer monthlyFee;

    /**
     * 月數
     */
    private Integer month;

    /**
     * 天數
     */
    private Integer day;

    /**
     * 保險費用
     */
    private Integer insurance;

    /**
     * 代步車費用
     */
    private Integer replacementCarFee;

    /**
     * 應收自負額
     */
    @JsonProperty("arcarLossAmt")
    private Integer aRCarLossAmt;

    /**
     * 實收自付額
     */
    private int carLossAmt;

    //==============罰金使用==============
    /**
     * 延後天數
     */
    private Integer delayDays;

    /**
     * 罰金產生原因
     */
    private String fineFrom;

    /**
     * 預估應收金額
     */
    private Integer originAmount;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 折扣原因
     */
    private String reason;

    /**
     * 主管是否同意
     */
    private Boolean isAgree;

    /**
     * 營業溝通議價人員
     */
    private String adminId;

    /**
     * 主管人員
     */
    private String managerId;

    /**
     * 裁決說明
     */
    private String decideRemark;

    /**
     * 原始送審折扣
     */
    private Integer originalDiscount;

    /**
     * 充電儲值金
     */
    private Integer chargingPoint;

    /**
     * 補收里程
     */
    private Integer addMileage;

    /**
     * 周邊商品原始單價
     */
    private Integer unitPrice;

    /**
     * 周邊商品實際單價
     */
    private Integer actualUnitPrice;

    /**
     * 周邊商品數量
     */
    private Integer quantity;

    /**
     * 省下實際里程數
     */
    public int getSaveRealMileage() {
        int discountMileage = Optional.ofNullable(getDiscountMileage()).orElse(0);
        return Math.min(getUseMileage(), discountMileage);
    }

    public int getUseMileage() {
        int startMileage = Optional.ofNullable(getStartMileage()).orElse(0);
        int endMileage = Optional.ofNullable(getEndMileage()).orElse(0);
        return endMileage - startMileage;
    }

    /**
     * 省下實際里程費
     */
    private double getSaveRealMileageFee() {
        return getSaveRealMileage() * Optional.ofNullable(mileageFee).orElse(0d);
    }

    /**
     * 創建 PriceInfoDetail 的深拷貝
     * 用於變更檢測，確保能正確比較原始狀態和修改後狀態
     */
    public PriceInfoDetail deepCopy() {
        PriceInfoDetail copy = new PriceInfoDetail();

        // 複製所有欄位
        copy.setStartMileage(this.startMileage);
        copy.setEndMileage(this.endMileage);
        copy.setDiscountMileage(this.discountMileage);
        copy.setOriginalDiscountMileage(this.originalDiscountMileage);
        copy.setRenewDiscountMileage(this.renewDiscountMileage);
        copy.setRentalDiscountMileage(this.rentalDiscountMileage);
        copy.setTotalMileage(this.totalMileage);
        copy.setMileageFee(this.mileageFee);
        copy.setMonthlyFee(this.monthlyFee);
        copy.setMonth(this.month);
        copy.setDay(this.day);
        copy.setInsurance(this.insurance);
        copy.setReplacementCarFee(this.replacementCarFee);
        copy.setARCarLossAmt(this.aRCarLossAmt);
        copy.setCarLossAmt(this.carLossAmt);
        copy.setDelayDays(this.delayDays);
        copy.setFineFrom(this.fineFrom);
        copy.setOriginAmount(this.originAmount);
        copy.setDiscount(this.discount);
        copy.setReason(this.reason);
        copy.setIsAgree(this.isAgree);
        copy.setAdminId(this.adminId);
        copy.setManagerId(this.managerId);
        copy.setDecideRemark(this.decideRemark);
        copy.setOriginalDiscount(this.originalDiscount);
        copy.setChargingPoint(this.chargingPoint);
        copy.setAddMileage(this.addMileage);
        copy.setUnitPrice(this.unitPrice);
        copy.setActualUnitPrice(this.actualUnitPrice);
        copy.setQuantity(this.quantity);

        return copy;
    }
}
