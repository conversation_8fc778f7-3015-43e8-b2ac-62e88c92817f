package com.carplus.subscribe.model.finbus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.Instant;

/**
 * 交易項目
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionItemReq {

    @Schema(description = "交易項目類型", example = "收款=CHARGE, 退款=REFUND")
    @NotNull(message = "交易項目類型不能為空")
    @Pattern(regexp = "^CHARGE|^REFUND$", message = "無效的交易類型: CHARGE, REFUND")
    private String type; // 類型: 收款=CHARGE, 退款=REFUND

    @Schema(description = "交易項目代碼", example = "租金(訂閱用)=RENT_SUBSCRIBE, eTag=ETAG, 保證金=MARGIN, 周邊商品=MERCHANDISE")
    @NotBlank(message = "交易項目代碼不能為空")
    private String code; // 交易項目代碼

    @Schema(description = "交易項目金額")
    @NotNull(message = "交易項目金額不能為空")
    @Min(value = 1, message = "交易項目金額最少為 1")
    private Integer amount;

    @Schema(description = "分攤起日")
    private Instant apportionStartDate;

    @Schema(description = "分攤迄日")
    private Instant apportionEndDate;
}
