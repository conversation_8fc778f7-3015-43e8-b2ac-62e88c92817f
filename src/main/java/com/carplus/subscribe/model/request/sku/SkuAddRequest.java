package com.carplus.subscribe.model.request.sku;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "新增周邊商品請求")
public class SkuAddRequest {

    @NotBlank(message = "商品編號不可為空")
    @Schema(description = "商品編號")
    private String code;

    @NotBlank(message = "商品類型不可為空")
    @Schema(description = "商品類型")
    private String type;

    @NotBlank(message = "商品名稱不可為空")
    @Schema(description = "商品名稱")
    private String name;

    @NotNull(message = "商品單價不可為空")
    @Min(value = 0, message = "商品單價不可小於0")
    @Schema(description = "商品單價")
    private Integer unitPrice;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品圖片路徑")
    private String imgPath;

    @Schema(description = "是否顯示於官網")
    private Boolean isOfficial;

    @Schema(description = "是否顯示於收銀台")
    private Boolean isCashier;
}
