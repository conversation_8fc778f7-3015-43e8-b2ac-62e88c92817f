package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.enums.CarReady;
import com.carplus.subscribe.enums.OrderPlatform;
import com.carplus.subscribe.model.CompanyDriver;
import com.carplus.subscribe.model.Driver;
import com.carplus.subscribe.model.EmpMileageDiscount;
import com.carplus.subscribe.model.ReferInfo;
import com.carplus.subscribe.model.config.YesChargingPoint;
import com.carplus.subscribe.model.invoice.Invoice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
public class ContractCreateReq {
    @NotNull(message = "車牌號碼不可為空")
    @Schema(description = "車牌號碼")
    private String plateNo;
    @NotNull(message = "方案不可為空")
    @Schema(description = "方案")
    private Integer carLevel;
    @Schema(description = "駕駛資訊")
    private Driver driver;
    @NotNull(message = "出車站點不可為空")
    @Schema(description = "出車站點")
    private String departStationCode;
    @NotNull(message = "還車站點不可為空")
    @Schema(description = "還車站點")
    private String returnStationCode;
    @Schema(description = "合約預計開始時間")
    private Instant expectStartDate;
    @Schema(description = "主約編號")
    private String mainContractNo;
    @Schema(description = "是否投保免責險")
    private boolean disclaimer;
    @Schema(description = "是否代步車")
    private boolean replacement;
    @Schema(description = "是否投保溢價險")
    private boolean premium;
    @Schema(description = "發票資訊")
    private Invoice invoice;
    @Schema(description = "訂單來源")
    private Integer custSource;
    @Schema(description = "訂單平台來源")
    private OrderPlatform orderPlatform;
    @Schema(description = "法人承租人")
    private CompanyDriver companyDriver;
    @Schema(description = "每季里程數折扣")
    private Map<Integer, EmpMileageDiscount> mileageDiscounts;
    @Schema(description = "備車註記")
    private CarReady carReady;
    @Schema(description = "介紹人")
    private ReferInfo referInfo;
    @Schema(description = "訂單註記")
    private String remark;
    @Schema(description = "客戶備註")
    private String custRemark;
    @Schema(description = "訂閱月份")
    private int month;
    @Schema(description = "電子訂單預覽版本")
    private String eContractTempVerId;
    @Schema(description = "充電儲值金")
    private YesChargingPoint yesChargingPoint;
    @Valid
    @Schema(description = "加購周邊商品列表")
    private List<MerchandiseReq> merchList;
}
