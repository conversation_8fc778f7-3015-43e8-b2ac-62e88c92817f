package com.carplus.subscribe.model.request.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "周邊商品")
public class MerchandiseReq {

    @NotBlank(message = "周邊商品代碼不可為空")
    @Schema(description = "周邊商品代碼")
    private String skuCode;

    @Min(value = 1, message = "商品實際單價必須大於0")
    @Schema(description = "商品實際單價")
    private Integer actualUnitPrice;

    @NotNull(message = "商品數量不可為空")
    @Min(value = 1, message = "商品數量必須大於0")
    @Schema(description = "商品數量")
    private Integer quantity;

    @Min(value = 1, message = "期數必須大於0")
    @Max(value = 4, message = "期數不可超過4")
    @Schema(description = "期數")
    private Integer stage;
}
