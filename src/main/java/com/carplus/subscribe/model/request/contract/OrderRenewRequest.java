package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.config.mapper.SubscribeRenewMonthDeserializer;
import com.carplus.subscribe.enums.OrderPlatform;
import com.carplus.subscribe.enums.SubscribeRenewMonth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OrderRenewRequest implements AutoCreditAware {

    @JsonDeserialize(using = SubscribeRenewMonthDeserializer.class)
    @Schema(description = "續約訂閱月份")
    private SubscribeRenewMonth month;

    @Schema(description = "使用者編號")
    private int acctId;

    @Schema(description = "訂單平台來源")
    private OrderPlatform orderPlatform;

    @Schema(description = "是否需要自動授信")
    private boolean needAutoCredit = true;

    @Schema(description = "不需要自動授信的原因")
    private String autoCreditBypassReason;
}
