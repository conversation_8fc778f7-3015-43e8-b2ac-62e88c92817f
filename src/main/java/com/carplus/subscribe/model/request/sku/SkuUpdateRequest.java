package com.carplus.subscribe.model.request.sku;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "更新周邊商品資料")
public class SkuUpdateRequest {

    @Pattern(regexp = "^(?!\\s*$).+", message = "商品類型不可為空白")
    @Schema(description = "商品類型")
    private String type;

    @Pattern(regexp = "^(?!\\s*$).+", message = "商品名稱不可為空白")
    @Schema(description = "商品名稱")
    private String name;
    
    @Min(value = 0, message = "商品單價不可小於0")
    @Schema(description = "商品單價")
    private Integer unitPrice;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品圖片路徑")
    private String imgPath;

    @Schema(description = "是否顯示於官網")
    private Boolean isOfficial;

    @Schema(description = "是否顯示於收銀台")
    private Boolean isCashier;
}
