package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@AllArgsConstructor
@Builder
@FieldNameConstants
public class PaymentInfo {
    @Schema(description = "請款-分攤部門別ID")
    private String paymentDepartmentCode;

    @Schema(description = "請款-分攤部門別ID")
    private String paymentDepartmentName;

}
