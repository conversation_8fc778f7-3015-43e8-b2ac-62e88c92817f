package com.carplus.subscribe.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 標記需要追蹤價格資訊更新人員的控制器方法
 * 
 * 使用此註解的方法將自動：
 * 1. 從請求標頭中提取 memberId
 * 2. 追蹤方法執行過程中對 OrderPriceInfo 的變更
 * 3. 自動設定 updator 欄位
 * 
 * 注意：
 * - 只有在實際發生變更時才會更新 updator
 * - 需要方法參數中包含 @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId
 * - 適用於修改價格資訊的 API 端點
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TrackPriceInfoUpdator {
    
    /**
     * 是否為必須有 memberId 的方法
     * 如果為 true，當沒有 memberId 時會記錄警告
     * 如果為 false，沒有 memberId 時會靜默跳過追蹤
     * 
     * @return 是否必須有 memberId，預設為 true
     */
    boolean required() default true;
    
    /**
     * 追蹤的描述，用於日誌記錄
     * 
     * @return 追蹤描述
     */
    String description() default "";
}
