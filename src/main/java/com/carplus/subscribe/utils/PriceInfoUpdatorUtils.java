package com.carplus.subscribe.utils;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 價格資訊更新人員追蹤工具類
 */
@Slf4j
public class PriceInfoUpdatorUtils {

    /**
     * 檢查是否需要更新 updator 欄位
     * 只有在 memberId 存在且價格資訊有實際變更時才更新
     *
     * @param memberId 會員編號
     * @param oldOrderPriceInfo 舊的價格資訊
     * @param newOrderPriceInfo 新的價格資訊
     * @return 是否需要更新 updator
     */
    public static boolean shouldUpdateUpdator(String memberId, OrderPriceInfo oldOrderPriceInfo, OrderPriceInfo newOrderPriceInfo) {
        // 如果 memberId 為空，不更新 updator
        if (StringUtils.isBlank(memberId)) {
            return false;
        }

        // 如果是新建的價格資訊，需要設定 updator
        if (oldOrderPriceInfo == null) {
            return true;
        }

        // 檢查是否有實際的價格變更
        return hasPriceInfoChanged(oldOrderPriceInfo, newOrderPriceInfo);
    }

    /**
     * 檢查價格資訊是否有變更
     *
     * @param oldOrderPriceInfo 舊的價格資訊
     * @param newOrderPriceInfo 新的價格資訊
     * @return 是否有變更
     */
    public static boolean hasPriceInfoChanged(OrderPriceInfo oldOrderPriceInfo, OrderPriceInfo newOrderPriceInfo) {
        if (oldOrderPriceInfo == null || newOrderPriceInfo == null) {
            return true;
        }

        // 檢查主要價格欄位是否有變更
        return !Objects.equals(oldOrderPriceInfo.getAmount(), newOrderPriceInfo.getAmount())
            || !Objects.equals(oldOrderPriceInfo.getCategory(), newOrderPriceInfo.getCategory())
            || !Objects.equals(oldOrderPriceInfo.getType(), newOrderPriceInfo.getType())
            || !Objects.equals(oldOrderPriceInfo.getStage(), newOrderPriceInfo.getStage())
            || !Objects.equals(oldOrderPriceInfo.getLastPayDate(), newOrderPriceInfo.getLastPayDate())
            || !Objects.equals(oldOrderPriceInfo.getReceivableDate(), newOrderPriceInfo.getReceivableDate())
            || !Objects.equals(oldOrderPriceInfo.getSkuCode(), newOrderPriceInfo.getSkuCode())
            || hasInfoDetailChanged(oldOrderPriceInfo, newOrderPriceInfo);
    }

    /**
     * 檢查 InfoDetail 是否有變更
     *
     * @param oldOrderPriceInfo 舊的價格資訊
     * @param newOrderPriceInfo 新的價格資訊
     * @return InfoDetail 是否有變更
     */
    private static boolean hasInfoDetailChanged(OrderPriceInfo oldOrderPriceInfo, OrderPriceInfo newOrderPriceInfo) {
        // 如果其中一個為 null，另一個不為 null，則認為有變更
        if (oldOrderPriceInfo.getInfoDetail() == null && newOrderPriceInfo.getInfoDetail() != null) {
            return true;
        }
        if (oldOrderPriceInfo.getInfoDetail() != null && newOrderPriceInfo.getInfoDetail() == null) {
            return true;
        }
        if (oldOrderPriceInfo.getInfoDetail() == null) {
            return false;
        }

        // 比較 InfoDetail 的主要欄位
        PriceInfoDetail oldDetail = oldOrderPriceInfo.getInfoDetail();
        PriceInfoDetail newDetail = newOrderPriceInfo.getInfoDetail();

        return !Objects.equals(oldDetail.getStartMileage(), newDetail.getStartMileage())
            || !Objects.equals(oldDetail.getEndMileage(), newDetail.getEndMileage())
            || !Objects.equals(oldDetail.getDiscountMileage(), newDetail.getDiscountMileage())
            || !Objects.equals(oldDetail.getTotalMileage(), newDetail.getTotalMileage())
            || !Objects.equals(oldDetail.getMileageFee(), newDetail.getMileageFee())
            || !Objects.equals(oldDetail.getMonthlyFee(), newDetail.getMonthlyFee())
            || !Objects.equals(oldDetail.getMonth(), newDetail.getMonth())
            || !Objects.equals(oldDetail.getDay(), newDetail.getDay())
            || !Objects.equals(oldDetail.getInsurance(), newDetail.getInsurance())
            || !Objects.equals(oldDetail.getReplacementCarFee(), newDetail.getReplacementCarFee())
            || !Objects.equals(oldDetail.getARCarLossAmt(), newDetail.getARCarLossAmt())
            || !Objects.equals(oldDetail.getCarLossAmt(), newDetail.getCarLossAmt())
            || !Objects.equals(oldDetail.getOriginAmount(), newDetail.getOriginAmount())
            || !Objects.equals(oldDetail.getDiscount(), newDetail.getDiscount())
            || !Objects.equals(oldDetail.getReason(), newDetail.getReason())
            || !Objects.equals(oldDetail.getChargingPoint(), newDetail.getChargingPoint())
            || !Objects.equals(oldDetail.getUnitPrice(), newDetail.getUnitPrice())
            || !Objects.equals(oldDetail.getActualUnitPrice(), newDetail.getActualUnitPrice())
            || !Objects.equals(oldDetail.getQuantity(), newDetail.getQuantity());
    }

    /**
     * 設定 updator 欄位
     *
     * @param orderPriceInfo 價格資訊
     * @param memberId 會員編號
     */
    public static void setUpdator(OrderPriceInfo orderPriceInfo, String memberId) {
        if (orderPriceInfo != null && StringUtils.isNotBlank(memberId)) {
            orderPriceInfo.setUpdator(memberId);
            log.debug("設定價格資訊 {} 的更新人員為: {}", orderPriceInfo.getId(), memberId);
        }
    }

    /**
     * 條件性設定 updator 欄位
     * 只有在滿足更新條件時才設定
     *
     * @param oldOrderPriceInfo 舊的價格資訊
     * @param newOrderPriceInfo 新的價格資訊
     * @param memberId 會員編號
     */
    public static void setUpdatorIfChanged(OrderPriceInfo oldOrderPriceInfo, OrderPriceInfo newOrderPriceInfo, String memberId) {
        if (shouldUpdateUpdator(memberId, oldOrderPriceInfo, newOrderPriceInfo)) {
            setUpdator(newOrderPriceInfo, memberId);
        }
    }
}
