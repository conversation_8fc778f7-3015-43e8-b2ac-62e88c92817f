package com.carplus.subscribe.utils;

import com.carplus.subscribe.aspect.PriceInfoUpdatorTrackingAspect;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 價格資訊追蹤輔助類
 * 
 * 提供便利方法來整合 AOP 追蹤功能與現有的服務層代碼
 */
@Component
@Slf4j
public class PriceInfoTrackingHelper {

    /**
     * 在修改 OrderPriceInfo 之前記錄其原始狀態
     * 
     * @param orderPriceInfo 要修改的價格資訊
     */
    public static void recordBeforeUpdate(OrderPriceInfo orderPriceInfo) {
        if (orderPriceInfo != null) {
            PriceInfoUpdatorTrackingAspect.recordOriginalState(orderPriceInfo);
        }
    }

    /**
     * 在修改 OrderPriceInfo 列表之前記錄其原始狀態
     * 
     * @param orderPriceInfos 要修改的價格資訊列表
     */
    public static void recordBeforeUpdate(List<OrderPriceInfo> orderPriceInfos) {
        if (orderPriceInfos != null) {
            orderPriceInfos.forEach(PriceInfoTrackingHelper::recordBeforeUpdate);
        }
    }

    /**
     * 在修改 OrderPriceInfo 之後應用 updator 追蹤
     * 
     * @param orderPriceInfo 已修改的價格資訊
     */
    public static void applyAfterUpdate(OrderPriceInfo orderPriceInfo) {
        if (orderPriceInfo != null) {
            PriceInfoUpdatorTrackingAspect.applyUpdatorTracking(orderPriceInfo);
        }
    }

    /**
     * 在修改 OrderPriceInfo 列表之後應用 updator 追蹤
     * 
     * @param orderPriceInfos 已修改的價格資訊列表
     */
    public static void applyAfterUpdate(List<OrderPriceInfo> orderPriceInfos) {
        if (orderPriceInfos != null) {
            PriceInfoUpdatorTrackingAspect.applyUpdatorTracking(orderPriceInfos);
        }
    }

    /**
     * 為新建的 OrderPriceInfo 應用 updator 追蹤
     * 
     * @param orderPriceInfo 新建的價格資訊
     */
    public static void applyForNewRecord(OrderPriceInfo orderPriceInfo) {
        if (orderPriceInfo != null) {
            PriceInfoUpdatorTrackingAspect.applyUpdatorTracking(orderPriceInfo);
        }
    }

    /**
     * 為新建的 OrderPriceInfo 列表應用 updator 追蹤
     * 
     * @param orderPriceInfos 新建的價格資訊列表
     */
    public static void applyForNewRecords(List<OrderPriceInfo> orderPriceInfos) {
        if (orderPriceInfos != null) {
            PriceInfoUpdatorTrackingAspect.applyUpdatorTracking(orderPriceInfos);
        }
    }

    /**
     * 包裝更新操作，自動處理追蹤
     * 
     * @param orderPriceInfo 要更新的價格資訊
     * @param updateAction 更新操作
     * @return 更新後的價格資訊
     */
    public static OrderPriceInfo wrapUpdate(OrderPriceInfo orderPriceInfo, java.util.function.Function<OrderPriceInfo, OrderPriceInfo> updateAction) {
        recordBeforeUpdate(orderPriceInfo);
        OrderPriceInfo result = updateAction.apply(orderPriceInfo);
        applyAfterUpdate(result);
        return result;
    }

    /**
     * 包裝批量更新操作，自動處理追蹤
     * 
     * @param orderPriceInfos 要更新的價格資訊列表
     * @param updateAction 更新操作
     * @return 更新後的價格資訊列表
     */
    public static List<OrderPriceInfo> wrapBatchUpdate(List<OrderPriceInfo> orderPriceInfos, 
                                                       java.util.function.Function<List<OrderPriceInfo>, List<OrderPriceInfo>> updateAction) {
        recordBeforeUpdate(orderPriceInfos);
        List<OrderPriceInfo> result = updateAction.apply(orderPriceInfos);
        applyAfterUpdate(result);
        return result;
    }
}
