package com.carplus.subscribe.service;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.mapper.contractinfo.ContractAddReqMapper;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.model.lrental.ContractAddReq;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MileageFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Slf4j
@Service
public class LrentalContractService {

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private CarsService carsService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private BuChangeService buChangeService;

    @Autowired
    private ContractAddReqMapper contractAddReqMapper;

    /**
     * 檢查長租契約日期是否與訂單起迄日期一致
     *
     * @param lrentalContractNo 長租契約編號
     * @param orderNo           訂單編號
     * @param startDate         開始日期
     * @param endDate           結束日期
     * @param expectStartDate   預期開始日期
     * @param expectEndDate     預期結束日期
     * @param typeDescription   訂單類型描述(用於日誌)
     * @return 長租契約資訊, 如果日期不一致需要重建時返回, 否則返回null
     */
    public Optional<ContractSearchRep> getLrentalContractIfDateMismatched(String lrentalContractNo,
                                                                          String orderNo,
                                                                          Instant startDate,
                                                                          Instant endDate,
                                                                          Instant expectStartDate,
                                                                          Instant expectEndDate,
                                                                          String typeDescription) {

        if (StringUtils.isBlank(lrentalContractNo)) {
            return Optional.empty();
        }

        ContractSearchRep contractInfo;
        try {
            contractInfo = lrentalServer.getContractInfo(lrentalContractNo);
        } catch (Exception e) {
            log.error("{} {} 檢查長租契約日期失敗", typeDescription, orderNo, e);
            return Optional.empty();
        }

        if (contractInfo == null) {
            return Optional.empty();
        }

        // 取得訂單起迄日期並轉換為民國年格式
        String orderStartDateStr = DateUtil.transferADDateToMinguoDate(
            Optional.ofNullable(startDate).orElse(expectStartDate)
        );
        String orderEndDateStr = DateUtil.transferADDateToMinguoDate(
            Optional.ofNullable(endDate).orElse(expectEndDate)
        );

        // 比對長租契約日期
        String contractStartDateStr = contractInfo.getDadt1().trim();
        String contractEndDateStr = contractInfo.getDadt2().trim();

        // 起迄日期是否一致? 若一致，則返空
        if (contractStartDateStr.equals(orderStartDateStr) && contractEndDateStr.equals(orderEndDateStr)) {
            return Optional.empty();
        }

        // 起迄日期不一致，記錄日誌並返回長租契約資訊
        log.info("{} {} 長租契約日期與訂單起迄日期不一致，進行重建。長租契約日期: {} ~ {}, 訂單起迄日期: {} ~ {}",
            typeDescription, orderNo,
            contractStartDateStr, contractEndDateStr,
            orderStartDateStr, orderEndDateStr);

        return Optional.of(contractInfo);
    }

    public Optional<LrentalContractRequest> prepareLrentalContractRecreationRequest(IOrder order) {

        if (!order.getIsNewOrder() || !CarsUtil.isCarPlusCar(order.getPlateNo())) {
            return Optional.empty();
        }

        return getLrentalContractIfDateMismatched(
                order.getLrentalContractNo(),
                order.getOrderNo(),
                order.getStartDate(),
                order.getEndDate(),
                order.getExpectStartDate(),
                order.getExpectEndDate(),
                order.getTypeDescription()
            ).map(contractInfo -> {
                // 建立新的契約請求
                LrentalContractRequest request = new LrentalContractRequest();
                request.setOrderNo(order.getOrderNo());
                request.setReplaceCodes(Arrays.asList(contractInfo.getDachang().split("")));
                // 取得原契約備註並更新日期
                request.setMemo(getUpdatedLrentalContractNote(contractInfo.getNote(), order));

                // 先清空訂單長租契約編號，否則會拋錯(已建立長租契約)
                order.setLrentalContractNo(null);

                return request;
            });
    }

    /**
     * 更新合約備註中的日期
     * 格式範例: "note": "SL／新單／中古車\n24120741311：2024/12/22 - 2025/03/21（租期3個月）\n"
     */
    private String getUpdatedLrentalContractNote(String lrentalContractNote, IOrder order) {
        if (StringUtils.isBlank(lrentalContractNote)) {
            return "";
        }

        String[] lines = lrentalContractNote.split("\\n+");
        if (lines.length < 2) {
            return lrentalContractNote;
        }

        if (order instanceof Orders) {
            return updateOrdersNote(lines, (Orders) order);
        } else if (order instanceof DealerOrder) {
            return updateDealerOrderNote(lines, (DealerOrder) order);
        }

        return lrentalContractNote;
    }

    private String updateOrdersNote(String[] lines, Orders orders) {
        // 合約日期行格式: C + 數字：日期 - 日期
        final String CONTRACT_LINE_PATTERN = "^C\\d+：\\d{4}/\\d{2}/\\d{2} - \\d{4}/\\d{2}/\\d{2}$";
        // 訂單日期行格式: B或M + 數字：日期 - 日期（租期個月）
        final String ORDER_LINE_PATTERN = "^[BM]\\d+：\\d{4}/\\d{2}/\\d{2} - \\d{4}/\\d{2}/\\d{2}（租期\\d+個月）$";

        // 找到合約日期行是第幾行
        int contractLineIndex = findMatchingLineIndex(lines, CONTRACT_LINE_PATTERN);
        // 更新合約日期行，若合約日期行不存在則不更新
        if (contractLineIndex >= 0) {
            Contract contract = orders.getContract();
            lines[contractLineIndex] = formatContractLine(contract);
        }

        // 從合約日期行的下一行開始找訂單日期行
        int orderLineIndex = findMatchingLineIndex(lines, ORDER_LINE_PATTERN, contractLineIndex + 1);
        // 更新訂單日期行，若訂單日期行不存在則不更新
        if (orderLineIndex >= 0) {
            lines[orderLineIndex] = formatOrderLine(orders.getOrderNo(), orders.getStartDate(),
                orders.getExpectStartDate(), orders.getEndDate(), orders.getExpectEndDate(), orders.getMonth());
        }

        return String.join("\n", lines);
    }

    private String updateDealerOrderNote(String[] lines, DealerOrder dealerOrder) {
        // 訂單日期行格式: [訂單編號]：日期 - 日期（租期個月）
        final String ORDER_LINE_PATTERN = "^[A-Z0-9]+：\\d{4}/\\d{2}/\\d{2} - \\d{4}/\\d{2}/\\d{2}（租期\\d+個月）$";

        // 找到訂單日期行是第幾行
        int orderLineIndex = findMatchingLineIndex(lines, ORDER_LINE_PATTERN);
        // 更新訂單日期行，若訂單日期行不存在則不更新
        if (orderLineIndex >= 0) {
            lines[orderLineIndex] = formatOrderLine(dealerOrder.getOrderNo(), dealerOrder.getDepartDate(),
                dealerOrder.getExpectDepartDate(), dealerOrder.getReturnDate(), dealerOrder.getExpectReturnDate(),
                dealerOrder.getSubscribeMonth());
        }

        return String.join("\n", lines);
    }

    private int findMatchingLineIndex(String[] lines, String pattern) {
        return findMatchingLineIndex(lines, pattern, 0);
    }

    private int findMatchingLineIndex(String[] lines, String pattern, int startIndex) {
        for (int i = startIndex; i < lines.length; i++) {
            if (lines[i].matches(pattern)) {
                return i;
            }
        }
        return -1;
    }

    private String formatContractLine(Contract contract) {
        String contractStartDate = DateUtil.getFormatString(
            Optional.ofNullable(contract.getStartDate()).orElse(contract.getExpectStartDate()),
            DateUtil.SLASH_FORMATTER_WITHOUT_TIME);
        String contractEndDate = DateUtil.getFormatString(
            contract.getExpectEndDate(),
            DateUtil.SLASH_FORMATTER_WITHOUT_TIME);
        return String.format("%s：%s - %s", contract.getContractNo(), contractStartDate, contractEndDate);
    }

    private String formatOrderLine(String orderNo, Instant startDate, Instant expectStartDate,
                                   Instant endDate, Instant expectEndDate, int month) {
        String start = DateUtil.getFormatString(
            Optional.ofNullable(startDate).orElse(expectStartDate),
            DateUtil.SLASH_FORMATTER_WITHOUT_TIME);
        String end = DateUtil.getFormatString(
            Optional.ofNullable(endDate).orElse(expectEndDate),
            DateUtil.SLASH_FORMATTER_WITHOUT_TIME);
        return String.format("%s：%s - %s（租期%d個月）", orderNo, start, end, month);
    }

    private Cars validateAndGetCars(String plateNo) {
        Cars cars = carsService.findByPlateNo(plateNo);
        if (cars == null) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        if (cars.isVirtualCar()) {
            throw new SubscribeException(LRENTAL_CONTRACT_CAN_NOT_CREATE_WITH_VIRTUAL_CAR);
        }
        return cars;
    }

    private CarBaseInfoSearchResponse validateAndGetCarBaseInfo(Cars cars) {
        CarBaseInfoSearchResponse carBaseInfo = crsService.getCar(cars.getPlateNo());
        if (carBaseInfo == null) {
            throw new SubscribeException(CRS_CAR_NOT_FOUND);
        }
        return carBaseInfo;
    }

    String createContract(DealerOrderQueryResponse dealerOrderQueryResponse, LrentalContractRequest request, String memberId) {
        if (StringUtils.isNotBlank(dealerOrderQueryResponse.getLrentalContractNo())) {
            throw new SubscribeException(LRENTAL_CONTRACT_ALREADY_CREATE);
        }
        Cars cars = validateAndGetCars(dealerOrderQueryResponse.getPlateNo());
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = validateAndGetCarBaseInfo(cars);
        if (BuIdEnum.isNotValidForSubscribe(carBaseInfoSearchResponse.getBuId())) {
            throw new SubscribeException(CAR_NOT_IN_SUBSCRIBE_SYSTEM);
        }
        buChangeService.checkSubscribeCarBuChange(carBaseInfoSearchResponse, cars, dealerOrderQueryResponse.getOrderNo());
        carsService.update(cars);
        ContractAddReq req = contractAddReqMapper.fromDealerOrder(dealerOrderQueryResponse, request, memberId, cars);
        PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBaseInfoSearchResponse.getCarNo());
        if (purchaseProjectCarSearchResponse != null) {
            req.validateProjectCar(purchaseProjectCarSearchResponse, dealerOrderQueryResponse.getSubscriptionInfo().getExpectReturnDate());
        }
        return lrentalServer.addContract(req);
    }

    String createContract(Orders orders, LrentalContractRequest request, String memberId) {
        Cars cars = validateAndGetCars(orders.getPlateNo());
        if (!CarsUtil.isCarPlusCar(cars.getVatNo())) {
            throw new SubscribeException(LRENTAL_CONTRACT_NEED_NOT_CREATE);
        }
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = validateAndGetCarBaseInfo(cars);
        if (Objects.equals(carBaseInfoSearchResponse.getBuId(), BuIdEnum.lRental.getCode())
            || StringUtils.isNotBlank(orders.getLrentalContractNo()) && Objects.equals(carBaseInfoSearchResponse.getBuId(), BuIdEnum.subscribe.getCode())) {
            throw new SubscribeException(LRENTAL_CONTRACT_ALREADY_CREATE);
        }
        if (BuIdEnum.isNotValidForSubscribe(carBaseInfoSearchResponse.getBuId())
            && !BuIdEnum.carCenter.getCode().equals(carBaseInfoSearchResponse.getBuId())) {
            throw new SubscribeException(NOT_FOR_SUBSCRIBE);
        }
        buChangeService.checkSubscribeCarBuChange(carBaseInfoSearchResponse, cars, orders.getOrderNo());
        carsService.update(cars);
        PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBaseInfoSearchResponse.getCarNo());
        Double mileageFee = priceInfoService.getPriceInfosByOrder(orders.getOrderNo(), MileageFee, Pay)
            .stream().findAny().map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_NOT_FUND));
        ContractAddReq req = contractAddReqMapper.fromOrders(orders, request, memberId, cars, mileageFee);
        if (purchaseProjectCarSearchResponse != null) {
            req.validateProjectCar(purchaseProjectCarSearchResponse, orders.getExpectEndDate());
        }
        return lrentalServer.addContract(req);
    }
}