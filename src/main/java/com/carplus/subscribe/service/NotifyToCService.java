package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.config.AppProperties;
import com.carplus.subscribe.constant.MaacConstant;
import com.carplus.subscribe.db.mysql.dao.UploadFileRepository;
import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.Notify;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.UploadFile;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.edm.EdmRequest;
import com.carplus.subscribe.model.notify.*;
import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.carplus.subscribe.model.notify.maac.Maac;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.server.*;
import com.carplus.subscribe.utils.PriceUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.constant.SMSConstant.*;
import static com.carplus.subscribe.enums.EdmType.*;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.utils.ContractUtils.CONTRACT_STATUS_PREDICATE_MAP;

@Service
@Slf4j
public class NotifyToCService {

    @Autowired
    private OrderService orderService;
    @Autowired
    private StationService stationService;
    @Autowired
    private CarsService carsService;
    @Autowired
    @Lazy
    private PriceInfoService priceInfoService;
    @Autowired
    private EdmServer edmServer;
    @Autowired
    private PushServer pushServer;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ShorterServer shorterServer;
    @Autowired
    private MaacCreatorService maacCreatorService;
    @Autowired
    @Lazy
    private EContractService eContractService;
    @Autowired
    private GcsService gcsService;

    @Value("${carplus.service.cashier}")
    private String cashier;
    @Value("${carplus.service.cashier}")
    private String cashierHost;
    @Autowired
    private ContractService contractService;
    @Autowired
    private UploadFileRepository uploadFileRepository;

    /**
     * 授信通過，通知繳費
     */
    @Async
    public void notifyCreditSuccess(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            long expiresDate = DateUtils.toDate(DateUtils.toLocalDateTime(new Date()).plusDays(2)).getTime();
            String name = user.getCustomerName();
            String subject2C = name + " - 格上租車‧閣下至上 - 預約訂單繳費通知";
            String shortUrl = shorterServer.createShort(expiresDate, String.format("%s/subscription/payment_pay?orderNo=%s", AppProperties.getOfficialHost(), order.getOrderNo()));
            String sms2C = String.format(CREDIT_SUCCESS, shortUrl);


            String sbf2C = generateEdmHtml(order, user, SUB_CREDIT_SUCCESS);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .build();
            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("授信通過，通知繳費 To C Fail", e);
            mattermostServer.notify("授信通過，通知繳費 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 授信未通過
     */
    @Async
    public void notifyCreditFail(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            String name = user.getCustomerName();
            String sms2C = String.format(CREDIT_FAIL, name, order.getOrderNo());

            String subject2C = name + " - 格上租車‧閣下至上 - 訂單審核未通過";
            String sbf2C = generateEdmHtml(order, user, SUB_CREDIT_FAIL);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .build();
            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("授信未通過 To C Fail", e);
            mattermostServer.notify("授信未通過 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 訂單取消
     */
    @Async
    public void notifyCancelOrder(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            String customerName = user.getCustomerName();
            String sms2C = String.format(CANCEL_ORDER, AppProperties.getStage(), customerName, order.getOrderNo());

            String subject2C = customerName + " - 格上租車‧閣下至上 - 預約訂單已取消";
            String sbf2C = generateEdmHtml(order, user, SUB_CANCEL_ORDER);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            MaacSubscribeNotify subNotify = MaacSubscribeNotify.builder()
                .category(NotifyCategory.SUB_CANCEL_ORDER)
                .baseDataDtoType(NotifyCategory.SUB_CANCEL_ORDER.getBaseDataDtoType())
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .pnpSettingId(MaacConstant.CANCEL_ORDER.getPnpSettingId())
                .linePushTemplateId(MaacConstant.CANCEL_ORDER.getLinePushTemplateId())
                .build();
            notify2CWithMaac(order, user, subNotify);
        } catch (Exception e) {
            log.error("授信未通過 To C Fail", e);
            mattermostServer.notify("授信未通過 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 每期費用付款提醒
     */
    @Async
    public void notifyPayStageFee(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getUnPaidPriceInfoByOrder(order.getOrderNo(), false);
            String customerName = user.getCustomerName();
            String lastPayDate = orderPriceInfoList.stream().min(Comparator.comparing(OrderPriceInfo::getLastPayDate))
                .map(OrderPriceInfo::getLastPayDate)
                .map(Instant::toEpochMilli)
                .map(Date::new).map(d -> DateUtils.toDateString(d, "yyyy/MM/dd")).orElse("截止繳款時間");
            String sms2C = String.format(OPEN_FOR_PAY_STAGE_FEE, AppProperties.getStage(), customerName, order.getOrderNo(), lastPayDate, AppProperties.getFullShortOfficialUrl());

            String subject2C = customerName + " - 格上租車‧閣下至上 - 繳費提醒";
            String sbf2C = generateEdmHtml(order, user, SUB_STAGE_PAY);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            MaacSubscribeNotify subNotify = MaacSubscribeNotify.builder()
                .category(NotifyCategory.OPEN_FOR_PAY_STAGE_FEE)
                .baseDataDtoType(NotifyCategory.OPEN_FOR_PAY_STAGE_FEE.getBaseDataDtoType())
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .pnpSettingId(MaacConstant.OPEN_FOR_PAY_STAGE_FEE.getPnpSettingId())
                .linePushTemplateId(MaacConstant.OPEN_FOR_PAY_STAGE_FEE.getLinePushTemplateId())
                .lastPayDate(lastPayDate)
                .build();
            notify2CWithMaac(order, user, subNotify);
        } catch (Exception e) {
            log.error("每期費用付款提醒 To C Fail", e);
            mattermostServer.notify("每期費用付款提醒 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 續約通知
     */
    public void notifyRenewOrder(@NonNull String orderNo) {
        Orders order = orderService.getOrder(orderNo);
        AuthUser authUser = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
        notifyRenewOrder(order, authUser);
    }

    /**
     * 續約通知
     */
    @Async
    public void notifyRenewOrder(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            String customerName = user.getCustomerName();
            long expiresDate = DateUtils.toDate(DateUtils.toLocalDateTime(new Date()).plusDays(10)).getTime();
            String renewUrl = shorterServer.createShort(expiresDate, String.format("%s/subscription/renew?orderNo=%s&utm_source=SU&utm_medium=SMS&utm_campaign=202307Subtoav335_Ren336", AppProperties.getOfficialHost(), order.getOrderNo()));
            String contractExpiredDate = DateUtils.toDateString(new Date(order.getExpectEndDate().toEpochMilli()), "yyyy/MM/dd");
            String sms2C = String.format(RENEW_ORDER, AppProperties.getStage(), customerName, contractExpiredDate, renewUrl);

            String subject2C = customerName + " - 格上租車‧閣下至上 - 續約提醒通知";
            String sbf2C = generateEdmHtml(order, user, EdmType.SUB_RENEW_CALL);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            MaacSubscribeNotify subNotify = MaacSubscribeNotify.builder()
                .category(NotifyCategory.SUB_RENEW_CALL)
                .baseDataDtoType(NotifyCategory.SUB_RENEW_CALL.getBaseDataDtoType())
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .pnpSettingId(MaacConstant.RENEW_CALL_ORDER.getPnpSettingId())
                .linePushTemplateId(MaacConstant.RENEW_CALL_ORDER.getLinePushTemplateId())
                .contractExpiredDate(contractExpiredDate)
                .renewUrl(renewUrl)
                .build();
            notify2CWithMaac(order, user, subNotify);
        } catch (Exception e) {
            log.error("續約通知 To C Fail", e);
            mattermostServer.notify("續約通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 續約成立通知
     */
    @Async
    public void notifyRenewConfirm(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            String name = user.getCustomerName();
            String sms2C = String.format(RENEW_CONFIRM, order.getOrderNo(), AppProperties.getFullShortOfficialUrl());

            String subject2C = name + " - 格上租車‧閣下至上 - 訂閱續約成功";
            String sbf2C = generateEdmHtml(order, user, SUB_RENEW_CONFIRM);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .build();
            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("續約成立通知 To C Fail", e);
            mattermostServer.notify("續約成立通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 還車通知
     */
    @Async
    public void notifyReturnOrder(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            String name = user.getCustomerName();
            String sms2C = String.format(RETURN_ORDER, order.getOrderNo(), DateUtils.toDateString(new Date(order.getExpectEndDate().toEpochMilli()), "yyyy/MM/dd"));

            String subject2C = name + " - 格上租車‧閣下至上 - 訂單到期通知";
            String sbf2C = generateEdmHtml(order, user, SUB_RETURN);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .build();
            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("續約通知 To C Fail", e);
            mattermostServer.notify("續約通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }


    /**
     * 訂單成立(已付保證金)
     */
    @Async
    public void notifySecurityDepositPaid(@NonNull Orders order, @NonNull AuthUser user) {
        try {
            String subject2C = user.getCustomerName() + " - 格上租車‧閣下至上 - 訂閱車輛確認信";

            String sms2C = String.format(SECURITY_PAID, order.getOrderNo(), AppProperties.getFullShortOfficialUrl());

            String sbf2C = generateEdmHtml(order, user, SUB_CREATE_ORDER);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .build();
            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("訂單成立(已付保證金)失敗通知 To C Fail", e);
            mattermostServer.notify("訂單成立(已付保證金)失敗通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 訂單變更
     */
    @Async
    public void notifyModifyOrder(@NonNull Orders order, @NonNull AuthUser user, @NonNull String plateNo) {
        try {
            String customerName = user.getCustomerName();
            String sms2C = String.format(MODIFY_ORDER, AppProperties.getStage(), customerName, order.getOrderNo(), AppProperties.getFullShortOfficialUrl());

            String subject2C = customerName + " - 格上租車‧閣下至上 - 訂單變更確認通知";
            String sbf2C = generateEdmHtml(order, user, SUB_UPDATE_ORDER);

            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            MaacSubscribeNotify subNotify = MaacSubscribeNotify.builder()
                .category(NotifyCategory.SUB_UPDATE_ORDER)
                .baseDataDtoType(NotifyCategory.SUB_UPDATE_ORDER.getBaseDataDtoType())
                .emailSubject2C(subject2C)
                .emailHtmlTemplate2C(content2C)
                .smsContent2C(sms2C)
                .pnpSettingId(MaacConstant.MODIFY_ORDER.getPnpSettingId())
                .linePushTemplateId(MaacConstant.MODIFY_ORDER.getLinePushTemplateId())
                .carInfo(plateNo)
                .build();
            notify2CWithMaac(order, user, subNotify);
        } catch (Exception e) {
            log.error("續約通知 To C Fail", e);
            mattermostServer.notify("續約通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 退款成功
     */
    @Async
    public void notifyRefund(@NonNull Orders order, AuthUser user, int amt) {
        try {
            String sms2C = String.format(REFUND_SUCCESS, AppProperties.getStage(), order.getOrderNo(), PriceUtils.formatWithThousandsSeparator(amt));
            MaacSubscribeNotify subNotify = MaacSubscribeNotify.builder()
                .category(NotifyCategory.TAPPAY_REFUND_SUCCESS)
                .baseDataDtoType(NotifyCategory.TAPPAY_REFUND_SUCCESS.getBaseDataDtoType())
                .smsContent2C(sms2C)
                .pnpSettingId(MaacConstant.TAPPAY_REFUND_SUCCESS.getPnpSettingId())
                .linePushTemplateId(MaacConstant.TAPPAY_REFUND_SUCCESS.getLinePushTemplateId())
                .build();
            notify2CWithMaac(order, user, subNotify);
        } catch (Exception e) {
            log.error("訂單成立(已付保證金)失敗通知 To C Fail", e);
            mattermostServer.notify("訂單成立(已付保證金)失敗通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 合約檔案寄送(起租) - 純文字
     */
    @Async
    public void notifyContractStatusGoing(@NonNull Orders order, AuthUser user, Map<String, String> downloadSignedUrls) {
        try {
            String name = user.getCustomerName();
            String plateNo = order.getPlateNo();
            String subject2C = "【" + order.getContractNo() + "】" + "格上訂閱 - 租賃契約(起租)";
            String sbf2C = buildECFileMailContent(order.getContractNo(), name, plateNo, downloadSignedUrls);
            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                    .emailSubject2C(subject2C)
                    .emailHtmlTemplate2C(content2C)
                    .build();

            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("續約通知 To C Fail", e);
            mattermostServer.notify("續約通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 合約檔案寄送(迄租) - 純文字
     */
    @Async
    public void notifyMainContractStatusComplete(@NonNull Orders order, AuthUser user, Map<String, String> downloadSignedUrls) {
        try {
            String name = user.getCustomerName();
            String plateNo = order.getPlateNo();
            String subject2C = "【" + order.getContractNo() + "】" + "格上訂閱 - 租賃契約(迄租)";
            String sbf2C = buildECFileMailContent(order.getContractNo(), name, plateNo, downloadSignedUrls);
            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                    .emailSubject2C(subject2C)
                    .emailHtmlTemplate2C(content2C)
                    .build();

            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("續約通知 To C Fail", e);
            mattermostServer.notify("續約通知 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 格上員工人為補寄合約檔案 - 純文字
     */
    @Async
    public void notifyContractManualReSend(@NonNull Orders order, AuthUser user, Map<String, String> downloadSignedUrls) {
        try {
            String name = user.getCustomerName();
            String plateNo = order.getPlateNo();
            String subject2C = "【" + order.getContract().getContractNo() + "】" + "格上訂閱 - 租賃契約";
            String sbf2C = buildECFileMailContent(order.getContractNo(), name, plateNo, downloadSignedUrls);
            String content2C = URLEncoder.encode(sbf2C, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                    .emailSubject2C(subject2C)
                    .emailHtmlTemplate2C(content2C)
                    .build();

            notify2C(order, user, subNotify);
        } catch (Exception e) {
            log.error("租賃契約 To C Fail", e);
            mattermostServer.notify("租賃契約 To C Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    public List<MailAttachment> notifyEContractFiles(@NonNull Orders order, String contractNo, AuthUser user, ContractStatus status) {
        List<Integer> uploadFileIds = eContractService.getEContracts(contractNo).stream()
                .filter(Objects::nonNull)
                .filter(ec -> CONTRACT_STATUS_PREDICATE_MAP.getOrDefault(status, e -> false).test(ec))
                .map(EContract::getUploadFileId)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<MailAttachment> attachments = new ArrayList<>();
        if (!uploadFileIds.isEmpty()) {
            log.info("ec upload files ids will be sent = {}", uploadFileIds);
            List<UploadFile> ecFiles = uploadFileRepository.getByMultiId(uploadFileIds);
            Map<String, String> downloadSignedUrls = new HashMap<>();
            ecFiles.forEach(uploadFile -> {
                GcsUrlRes gcsUrlRes = gcsService.getDownloadPresigned(user.getAcctId(), UploadFileKindEnum.CUSTOM_CONTRACT, uploadFile.getId(), Math.toIntExact(Duration.ofHours(24).toMinutes()));
                downloadSignedUrls.put(gcsUrlRes.getSignedUrls().get(0).getSignedUrl(), uploadFile.getDisplayFilename());
                attachments.add(
                        MailAttachment.builder()
                                .mediaType(gcsUrlRes.getSignedUrls().get(0).getMediaType())
                                .filename(uploadFile.getDisplayFilename())
                                .fileUrl(gcsUrlRes.getSignedUrls().get(0).getSignedUrl())
                                .build()
                );
            });

            if (!ecFiles.isEmpty()) {
                switch (status) {
                    case GOING:
                        log.info("contract status GOING : notify ec files");
                        notifyContractStatusGoing(order, user, downloadSignedUrls);
                        break;
                    case COMPLETE:
                        log.info("main contract status COMPLETE : notify ec files");
                        notifyMainContractStatusComplete(order, user, downloadSignedUrls);
                        break;
                    default:
                        log.info("no match contract status for send ec files");
                }
                return attachments;
            }
        }
        return attachments;
    }

    private String buildECFileMailContent(String contractNo, String name, String plateNo, Map<String, String> downloadSignedUrls) {
        return "<p>" + "格上租車感謝您的訂閱。" + "</p>"
                + "<ul>"
                    + "<li>承租人 : " + name + "</li>"
                    + "<li>承租車號 : " + plateNo + "</li>"
                    + "<li>契約編號 : " + contractNo + "</li>"
                    + "<li>契約下載連結 : "
                        + "<ul>"
                        + buildPreSignedUrlHTML(downloadSignedUrls)
                        + "</ul>"
                    + "</li>"
                + "</ul>"
                + "<p>" + "租賃契約檔案如附連結，開啟密碼為訂閱會員之證件號碼，"
                + "<span style=\"color: red;\">" + "連結將於24小時內失效" + "</span>"
                + "。"
                + "</p>";
    }

    private String buildPreSignedUrlHTML(Map<String, String> downloadPreSignedUrls) {
        StringBuilder sb = new StringBuilder();
        downloadPreSignedUrls.forEach((signedUrl, displayFileName) -> {
            sb.append("<li>")
                    .append("<a href=")
                    .append(signedUrl)
                    .append(">")
                    .append(displayFileName)
                    .append("</a>")
                    .append("</li>");
        });
        return sb.toString();
    }

    /**
     * 通知訂車人
     */
    private void notify2C(@NonNull Orders order, @NonNull AuthUser user, @NonNull SubscribeNotify subNotify) {
        //  sms
        if (StringUtils.isNotBlank(user.getMainCell()) && subNotify.getSmsContent2C() != null) {
            sendSms(order, user, subNotify);
        }

        // email
        if (StringUtils.isNotBlank(user.getEmail()) && StringUtils.isNotBlank(subNotify.getEmailHtmlTemplate2C())) {
            sendEmail(order, user, subNotify);
        }
    }

    private void sendSms(Orders order, AuthUser user, SubscribeNotify subNotify) {
        try {
            Sms s = Sms.builder()
                .nationalCode(Optional.ofNullable(user.getNationalCode()).orElse("886"))
                .msgcontent(subNotify.getSmsContent2C())
                .msgmemobile(user.getMainCell())
                .msgtype("0")
                .sender(HeaderDefine.SystemKind.SUB)
                .build();
            Notify notify = Notify.builder()
                .notifyType(NotifyType.SMS)
                .category(subNotify.getCategory())
                .status(NotifyStatus.pending)
                .orderNo(StringUtils.trim(order.getOrderNo()))
                .defDate(new Date())
                .notifyContent(objectMapper.writeValueAsString(s))
                .build();
            pushServer.notifyAndSave(notify);
        } catch (Exception e) {
            log.error("發送訂閱車SMS 2C通知失敗 訂單編號={}, 錯誤={}", order.getOrderNo(), e.getMessage());
        }
    }

    private void sendEmail(Orders order, AuthUser user, SubscribeNotify subNotify) {
        try {
            Email email = Email.builder()
                .withoutHtmlParams(true)
                .acctId(user.getAcctId())
                .receive(user.getEmail())
                .subject(StringUtils.trim(subNotify.getEmailSubject2C()))
                .content(subNotify.getEmailHtmlTemplate2C())
                .build();
            Notify n = Notify.builder()
                .notifyType(NotifyType.EMAIL)
                .category(subNotify.getCategory())
                .status(NotifyStatus.pending)
                .orderNo(StringUtils.trim(order.getOrderNo()))
                .defDate(new Date())
                .notifyContent(objectMapper.writeValueAsString(email))
                .build();
            pushServer.notifyAndSave(n);
        } catch (Exception e) {
            log.error("發送訂閱車EMAIL 2C通知失敗 訂單編號={}, 錯誤={}", order.getOrderNo(), e.getMessage());
        }
    }

    /**
     * 通知訂車人(使用漸強)
     */
    private void notify2CWithMaac(@NonNull Orders order, @NonNull AuthUser user, @NonNull MaacSubscribeNotify subNotify) {
        //  Maac PUSH / PNP / SMS
        if (StringUtils.isNotBlank(user.getMainCell()) && subNotify.getSmsContent2C() != null) {
            sendMaac(order, user, subNotify);
        }

        // email
        if (StringUtils.isNotBlank(user.getEmail()) && StringUtils.isNotBlank(subNotify.getEmailHtmlTemplate2C())) {
            sendEmail(order, user, subNotify);
        }
    }

    private void sendMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        try {
            Maac<? extends BaseDataDTO> maac =
                    maacCreatorService.createMaac(subNotify.getBaseDataDtoType(), order, user, subNotify);

            Notify notify = Notify.builder()
                .notifyType(NotifyType.MAAC)
                .category(subNotify.getCategory())
                .status(NotifyStatus.pending)
                .orderNo(StringUtils.trim(order.getOrderNo()))
                .defDate(new Date())
                .notifyContent(objectMapper.writeValueAsString(maac))
                .build();
            pushServer.notifyAndSave(notify);
        } catch (Exception e) {
            log.error("發送訂閱車SMS(漸強 MAAC) 2C通知失敗 訂單編號={}, 錯誤={}", order.getOrderNo(), e.getMessage());
        }
    }

    /**
     * 計算第一期費用
     */
    private Map<PriceInfoDefinition.PriceInfoCategory, Integer> calculateFirstStageFee(List<OrderPriceInfo> orderPriceInfoList) {
        Set<PriceInfoDefinition.PriceInfoCategory> targetCategories = EnumSet.of(MonthlyFee, Insurance, Dispatch, Replacement);
        return orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getStage() == 1 && targetCategories.contains(orderPriceInfo.getCategory()))
            .collect(Collectors.toMap(PriceInfoInterface::getCategory, PriceInfoInterface::getActualPrice, Integer::sum));
    }

    /**
     * 產生EDM範本
     */
    private String generateEdmHtml(Orders order, AuthUser user, EdmType edmType) {

        MainContract mainContract = order.getContract().getMainContract();
        CarBrandModelDTO car = carsService.getCarBrandModelDTOByCars(Collections.singletonList(mainContract.getPlateNo())).stream().findAny().get();


        EdmRequest edmRequest = EdmRequest.builder()
            .type(edmType)
            .orderNo(order.getOrderNo())
            .custName(user.getAcctName())
            .createDate(new Date(order.getInstantCreateDate().toEpochMilli()))
            .carModelName(car.getModel().getCarModelName())
            .carBrandName(car.getBrand().getBrandNameEn())
            .unpaidSecurityDeposit(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getUnpaidSecurityDeposit())
            .securityDeposit(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit())
            .monthlyFee(mainContract.getOriginalPriceInfo().getMonthlyFee())
            .discountMonthlyFee(mainContract.getOriginalPriceInfo().getDiscountMonthlyFee())
            .monthlyDiscounted(mainContract.getOriginalPriceInfo().isMonthlyDiscounted())
            .levelDiscounted(mainContract.getOriginalPriceInfo().isLevelDiscounted())
            .useMonthlyFee(mainContract.getOriginalPriceInfo().getUseMonthlyFee())
            .mileageFee(Optional.of(mainContract.getOriginalPriceInfo().getMileageFee()).orElse(0d))
            .insurance(mainContract.getOriginalPriceInfo().getDisclaimerFee())
            .replacement(mainContract.getOriginalPriceInfo().getReplacementCarFee())
            .months(order.getMonth())
            .status(OrderStatus.of(order.getStatus()).getName())
            .returnDate(new Date(order.getExpectEndDate().toEpochMilli()))
            .departDate(new Date(order.getExpectStartDate().toEpochMilli()))
            .stageMonth(3)
            .build();

        if (edmRequest.getMileageFee() == 0) {
            edmRequest.setMileageFee(mainContract.getOriginalPriceInfo().getOriginalMileageFee());
        }
        // 處理V1轉過來了里程優惠
        List<OrderPriceInfo> mileageFee = priceInfoService.getPriceInfosByOrder(order.getOrderNo(), MileageFee, PriceInfoDefinition.PriceInfoType.Pay);
        mileageFee.stream().forEach(opi -> {
            if (opi.getInfoDetail().getMileageFee() < edmRequest.getMileageFee()) {
                edmRequest.setMileageFee(opi.getInfoDetail().getMileageFee());
            }
        });
        if (Objects.equals(Boolean.FALSE, order.getContract().getDisclaimer())) {
            edmRequest.setInsurance(null);
        }
        if (Objects.equals(Boolean.FALSE, order.getContract().getReplacement())) {
            edmRequest.setReplacement(null);
        }

        switch (edmType) {
            // 設定第一期費用
            case SUB_CREATE_ORDER:
            case SUB_UPDATE_ORDER:
            case SUB_RENEW_CONFIRM:
                // Handle priceInfo calculations
                List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());
                Map<PriceInfoDefinition.PriceInfoCategory, Integer> firstStagePay = calculateFirstStageFee(orderPriceInfoList);
                edmRequest.setFirstStageMonthlyFee(firstStagePay.getOrDefault(MonthlyFee, 0));
                edmRequest.setFirstStageInsurance(firstStagePay.getOrDefault(Insurance, 0));
                edmRequest.setFirstReplacementCarFee(firstStagePay.getOrDefault(Replacement, 0));
                edmRequest.setDispatchFee(firstStagePay.getOrDefault(Dispatch, 0));

                // Also handle station information for these cases
                setStationInformation(edmRequest, mainContract, stationService);
                break;
            case SUB_CREDIT_SUCCESS:
            case SUB_STAGE_PAY:
            case SUB_RENEW_CALL:
            case SUB_RETURN:
                // Handle station information only
                setStationInformation(edmRequest, mainContract, stationService);
                break;
            default:
                // Handle any other edmType if needed
                break;
        }

        edmRequest.setStatus(EdmRequest.status(edmType, order));
        if (order.getMonth() < 3) {
            edmRequest.setStageMonth(order.getMonth());
        }

        edmRequest.setStageMonthlyFee(edmRequest.getStageMonth() * mainContract.getOriginalPriceInfo().getUseMonthlyFee());
        if (Objects.equals(Boolean.TRUE, order.getContract().getDisclaimer())) {
            edmRequest.setStageInsurance(edmRequest.getStageMonth() * mainContract.getOriginalPriceInfo().getDisclaimerFee());
        }
        if (Objects.equals(Boolean.TRUE, order.getContract().getReplacement())) {
            edmRequest.setStageReplacementCarFee(edmRequest.getStageMonth() * mainContract.getOriginalPriceInfo().getReplacementCarFee());
        }
        if (SUB_STAGE_PAY.equals(edmType)) {
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getUnPaidPriceInfoByOrder(order.getOrderNo(), false);
            edmRequest.setLastPayDate(orderPriceInfoList.stream().min(Comparator.comparing(OrderPriceInfo::getLastPayDate))
                .map(OrderPriceInfo::getLastPayDate)
                .map(Instant::toEpochMilli)
                .map(Date::new).orElse(null));
            edmRequest.setMonths(null);
        }

        if (SUB_STAGE_PAY.equals(edmType) || SUB_CREDIT_SUCCESS.equals(edmType)) {
            edmRequest.setCreateDate(null);
        }
        return edmServer.subscribe(edmRequest).getData().getToC();
    }

    private void setStationInformation(EdmRequest edmRequest, MainContract mainContract, StationService stationService) {
        Stations departStation = stationService.findByStationCode(mainContract.getDepartStationCode());
        Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());

        edmRequest.setDepartStationName(departStation.getStationName());
        edmRequest.setDepartStationTel(departStation.getTel());
        edmRequest.setDepartStationAddr(departStation.getAddr());
        edmRequest.setReturnStationName(returnStation.getStationName());
        edmRequest.setReturnStationTel(returnStation.getTel());
        edmRequest.setReturnStationAddr(returnStation.getAddr());
    }
}
