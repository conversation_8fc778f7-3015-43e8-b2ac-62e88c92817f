package com.carplus.subscribe.service;

import carplus.common.model.Page;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CarWishlistRepository;
import com.carplus.subscribe.db.mysql.entity.CarWishlist;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistCriteria;
import com.carplus.subscribe.model.request.carwishlist.CommonCarWishlistCriteria;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponse;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponseWithSnapshot;
import com.carplus.subscribe.server.AuthServer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CarWishlistService {

    private final CarWishlistRepository carWishlistRepository;
    private final CarsService carsService;
    private final AuthServer authServer;
    private final SubscribeLevelService subscribeLevelService;
    private final StationService stationService;

    public void add(Integer acctId, String plateNo) {

        AuthUser authUser = authServer.getUserWithRetry(acctId);

        Cars car = Optional.ofNullable(carsService.findByPlateNo(plateNo))
            .filter(Cars::isOrderableOfficially)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_NOT_FOUND));

        // 檢查是否存在未刪除的記錄
        if (carWishlistRepository.existsByAcctIdAndPlateNo(acctId, plateNo)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.CAR_ALREADY_IN_WISHLIST);
        }

        SubscribeLevel subscribeLevel = subscribeLevelService.determineSubscribeLevel(car);

        // 檢查是否存在軟刪除的記錄
        Optional<CarWishlist> existingWishlist = carWishlistRepository.findByAcctIdAndPlateNo(acctId, plateNo, true);
        CarWishlist carWishlist;

        if (existingWishlist.isPresent()) {
            // 恢復軟刪除的記錄
            carWishlist = existingWishlist.get();
            carWishlist.setUseMonthlyFee(car.getUseMonthlyFee(subscribeLevel));
            carWishlist.setMileageFee(subscribeLevel.getMileageFee());
            carWishlist.setSecurityDeposit(subscribeLevel.getSecurityDeposit());
            carWishlist.setType(subscribeLevel.getType());
            carWishlist.setTagIds(car.getTagIds());
            carWishlist.setLocationStationCode(car.getLocationStationCode());
            carWishlist.setDeletedAt(null);
        } else {
            // 檢查收藏清單數量限制
            long wishlistCount = carWishlistRepository.countByAcctIdAndDeletedAtIsNull(acctId);
            if (wishlistCount >= CarPlusConstant.CAR_WISHLIST_LIMIT_PER_USER) {
                throw new SubscribeException(SubscribeHttpExceptionCode.CAR_WISHLIST_LIMIT_EXCEEDED);
            }

            // 創建新記錄
            carWishlist = new CarWishlist(authUser.getAcctId(), car, subscribeLevel);
        }

        carWishlistRepository.save(carWishlist);
    }

    public void remove(Integer acctId, String plateNo) {

        CarWishlist carWishlist = carWishlistRepository.findByAcctIdAndPlateNo(acctId, plateNo, false)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_NOT_IN_WISHLIST));

        carWishlist.setDeletedAt(Instant.now());
        carWishlistRepository.save(carWishlist);
    }

    public <T extends CommonCarWishlistCriteria> Page<? extends CarWishlistResponse> searchByPage(T criteria) {
        int skip = criteria.getSkip();
        int limit = criteria.getLimit();

        long count = carWishlistRepository.count(criteria);
        if (count == 0) {
            return Page.of(0, Collections.emptyList(), skip, limit);
        }

        List<? extends CarWishlistResponse> list = searchPage(criteria, skip, limit);
        return Page.of(count, list, skip, limit);
    }

    private <T extends CommonCarWishlistCriteria> List<? extends CarWishlistResponse> searchPage(T criteria, int offset, int limit) {

        Map<String, Stations> stationMap = stationService.findAll().stream()
            .collect(Collectors.toMap(Stations::getStationCode, Function.identity()));

        List<CarWishlist> carWishlists = carWishlistRepository.findBySearch(criteria, offset, limit);
        Map<String, CarWishlist> carWishlistMap = carWishlists.stream()
            .collect(Collectors.toMap(CarWishlist::getPlateNo, Function.identity()));
        List<String> plateNos = carWishlists.stream()
            .map(CarWishlist::getPlateNo)
            .collect(Collectors.toList());

        // 根據 criteria 類型獲取 brandCode 和 carModelCode
        List<String> brandCodes = (criteria instanceof CarWishlistCriteria) ? ((CarWishlistCriteria) criteria).getBrandCodes() : null;
        List<String> carModelCodes = (criteria instanceof CarWishlistCriteria) ? ((CarWishlistCriteria) criteria).getCarModelCodes() : null;
        Map<String, CarResponse> carInfoMap = carsService.getCarInfoList(plateNos, brandCodes, carModelCodes).stream()
            .collect(Collectors.toMap(CarResponse::getPlateNo, Function.identity()));

        return plateNos.stream()
            .map(plateNo -> {
                CarWishlist carWishlist = carWishlistMap.get(plateNo);
                CarResponse carInfo = carInfoMap.get(plateNo);
                if (carInfo == null) {
                    return null;
                }
                if (criteria instanceof CarWishlistCriteria) {
                    return new CarWishlistResponseWithSnapshot(carWishlist, carInfo, stationMap);
                }
                return new CarWishlistResponse(carWishlist, carInfo, stationMap.get(carInfo.getLocationStationCode()));
            })
            .filter(Objects::nonNull) // 過濾掉可能的 null 值
            .collect(Collectors.toList());
    }
}
