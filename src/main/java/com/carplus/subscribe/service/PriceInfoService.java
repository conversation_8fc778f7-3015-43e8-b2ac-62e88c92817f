package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.PriceInfoConstant;
import com.carplus.subscribe.db.mysql.dao.OrderPriceInfoRepository;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.mapper.priceinfo.PriceInfoMapper;
import com.carplus.subscribe.model.*;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.config.YesChargingPoint;
import com.carplus.subscribe.model.order.CommonOrderPriceInfoResponse;
import com.carplus.subscribe.model.payment.AdditionalData;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.priceinfo.resp.*;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.model.request.contract.CalculateRequest;
import com.carplus.subscribe.model.request.contract.ContractCreateReq;
import com.carplus.subscribe.model.request.contract.MerchandiseReq;
import com.carplus.subscribe.model.request.dropoff.CarDropOffDiscountRequest;
import com.carplus.subscribe.model.request.order.OrdersCriteria;
import com.carplus.subscribe.model.request.priceinfo.*;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.carplus.subscribe.model.subscribelevel.MileageDiscount;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.PriceUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.SingletonMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.nio.charset.Charset;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.OrderStatus.DEPART;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static com.carplus.subscribe.utils.CarsUtil.getLastPayDate;
import static java.time.temporal.ChronoUnit.*;

@Service
@Slf4j
@Lazy
public class PriceInfoService {

    @Autowired
    private CarsService carsService;
    @Autowired
    private StationService stationService;
    @Autowired
    private ContractLogic contractLogic;
    @Autowired
    @Lazy
    private PriceInfoService self;
    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;
    @Autowired
    private OrderService orderService;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private CrsService crsService;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private MattermostServer mattermostServer;

    /**
     * 計算計價檔
     */
    public List<OrderPriceInfo> calculateOrderPrice(@NonNull Orders order) {
        long contractDiffMonth = DateUtil.calculateDiffMonth(Optional.ofNullable(order.getContract().getStartDate()).orElseGet(() -> order.getContract().getExpectStartDate()),
            order.getExpectEndDate());

        long mainContractDiffMonth = DateUtil.calculateDiffMonth(Optional.ofNullable(order.getContract().getMainContract().getStartDate()).orElseGet(() -> order.getContract().getMainContract().getExpectStartDate()),
            order.getExpectEndDate());

        Cars car = carsService.findByPlateNo(order.getPlateNo());
        if (Objects.isNull(car)) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }

        List<OrderPriceInfo> orderPriceInfos = new ArrayList<>();
        PriceInfo priceInfo = order.getContract().getMainContract().getOriginalPriceInfo();
        SubscribeLevel level = subscribeLevelService.findByLevel(order.getContract().getMainContract().getGivenCarLevel());
        Instant startDate = order.getExpectStartDate();
        int stage = 0;
        int remainingMonths = order.getMonth();

        // 保證金計算
        OrderPriceInfo securityPosit = calculateSecurityPosit(order.getContract().getMainContract());
        if (securityPosit.getAmount() > 0) {
            orderPriceInfos.add(securityPosit);
            securityPosit.setOrderNo(order.getOrderNo());
        }

        // 月費計算
        while (startDate.toEpochMilli() < order.getExpectEndDate().minus(5, DAYS).toEpochMilli() && stage < 4) {
            stage++;
            int seasonMonths = Math.min(3, remainingMonths);
            // 計算月費
            OrderPriceInfo orderPriceInfo = calculateMonthlyFee(priceInfo, seasonMonths, startDate, stage);
            orderPriceInfo.setOrderNo(order.getOrderNo());
            orderPriceInfos.add(orderPriceInfo);

            // 新車滿一年折價一千
            if (car.getCarState() == CarDefine.CarState.NEW && !order.getIsNewOrder() && (contractDiffMonth == 12 || order.getContract().getStage() > 1)) {
                int monthDiscountUnit = Double.valueOf(Math.min(mainContractDiffMonth / 12, 2)).intValue();
                OrderPriceInfo negativeOrderPriceInfo =
                    calculateDiscountMonthlyFee(seasonMonths, monthDiscountUnit,
                        startDate, stage);
                if (negativeOrderPriceInfo.getAmount() > 0) {
                    negativeOrderPriceInfo.setOrderNo(order.getOrderNo());
                    negativeOrderPriceInfo.setRefPriceInfo(orderPriceInfo);
                    orderPriceInfos.add(negativeOrderPriceInfo);
                }
            }

            // 計算保險
            OrderPriceInfo insuranceOrderPriceInfo = calculateInsurance(priceInfo, order.getContract().getDisclaimer(), order.getContract().getPremium(), seasonMonths, stage, startDate);
            if (insuranceOrderPriceInfo.getAmount() > 0) {
                insuranceOrderPriceInfo.setOrderNo(order.getOrderNo());
                orderPriceInfos.add(insuranceOrderPriceInfo);
            }

            // 計算代步車
            OrderPriceInfo replacementOrderPriceInfo = calculateReplacement(priceInfo, order.getContract().getReplacement(), seasonMonths, stage, startDate);
            if (replacementOrderPriceInfo.getAmount() > 0) {
                replacementOrderPriceInfo.setOrderNo(order.getOrderNo());
                orderPriceInfos.add(replacementOrderPriceInfo);
            }


            // 初始化里程費
            OrderPriceInfo mileageOrderPriceInfo = initCalculateMileageFeeV2(priceInfo, order, seasonMonths, startDate, stage, level);
            mileageOrderPriceInfo.setOrderNo(order.getOrderNo());
            orderPriceInfos.add(mileageOrderPriceInfo);
            startDate = startDate.atZone(DateUtils.ZONE_TPE).plus(seasonMonths, MONTHS).toInstant();

            remainingMonths -= seasonMonths;
        }

        // 跨區調度費計算
        if (order.getContract().getMainContract().getExpectStartDate().compareTo(order.getExpectStartDate()) == 0) {
            OrderPriceInfo dispatchOrderPriceInfo =
                calculateDispatch(priceInfo, order.getPlateNo(), order.getContract().getMainContract().getDepartStationCode(), order.getContract().getMainContract().getExpectStartDate());
            if (dispatchOrderPriceInfo.getAmount() > 0) {
                orderPriceInfos.add(dispatchOrderPriceInfo);
                dispatchOrderPriceInfo.setOrderNo(order.getOrderNo());
            }
        }

        if (!order.getIsNewOrder()) {
            Orders preOrder = contractLogic.getDepartOrdersByMainContract(order.getContract().getMainContractNo());
            if (preOrder != null && StringUtils.isNotBlank(preOrder.getSrentalParentOrderNo())) {
                AtomicReference<Double> mileageFee = new AtomicReference<>(priceInfo.getMileageFee() > 0 ? priceInfo.getMileageFee() : priceInfo.getOriginalMileageFee());
                getPriceInfosByOrder(preOrder.getOrderNo(), MileageFee, Pay).forEach(
                    opi -> {
                        double preMileage = Optional.ofNullable(opi).map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee).orElse(0d);
                        if (preMileage > 0 && preMileage < mileageFee.get()) {
                            mileageFee.set(preMileage);
                        }

                    }
                );
                orderPriceInfos.forEach(opi -> {
                    if (opi.getCategory() == MileageFee && opi.getType() == Pay.getCode()) {
                        Optional.ofNullable(opi.getInfoDetail()).ifPresent(detail -> detail.setMileageFee(mileageFee.get()));
                    }
                });
            }
        }

        return orderPriceInfos.stream()
            .sorted(Comparator.comparing(OrderPriceInfo::getCategory, Comparator.comparing(PriceInfoDefinition.PriceInfoCategory::getSortIndex))
                .thenComparing(OrderPriceInfo::getType))
            .collect(Collectors.toList());
    }

    /**
     * 試算計價檔
     */
    public List<OrderPriceInfoResponse> calculateOrderPrice(CalculateRequest request) {
        Cars car = carsService.findByPlateNo(request.getPlateNo());
        if (Objects.isNull(car)) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        SubscribeLevel initialSubscribeLevel = subscribeLevelService.findByLevel(request.getCarLevel());
        SubscribeLevel adoptedSubscribeLevel = subscribeLevelService.determineSubscribeLevel(car, initialSubscribeLevel);
        ContractCreateReq req = new ContractCreateReq();
        org.springframework.beans.BeanUtils.copyProperties(request, req, "month");
        req.setMonth(request.getMonth().getValue());
        Contract contract = contractLogic.generateContract(req, car, new AuthUser(), adoptedSubscribeLevel);
        Orders order = orderService.createOrderFromContract(contract, null, null, req.getMonth(), true,
            null, null, request.getYesChargingPoint(), req.getMerchList(), null, req.getOrderPlatform());
        order.setIsNewOrder(StringUtils.isBlank(request.getMainContractNo()));

        List<OrderPriceInfo> result = calculateOrderPrice(order);
        if (request.getYesChargingPoint() != null) {
            result.add(calculateYesCharging(order, request.getYesChargingPoint()));
        }

        // 處理加購商品費用
        List<MerchandiseReq> merchList = request.getMerchList();
        Map<String, Sku> skuMap = CollectionUtils.isNotEmpty(merchList)
            ? skuService.getMapByCodes(merchList.stream().map(MerchandiseReq::getSkuCode).collect(Collectors.toSet()))
            : Collections.emptyMap();
        if (!skuMap.isEmpty()) {
            result.addAll(calculateMerchandisePrices(order, merchList, skuMap));
        }

        Optional.ofNullable(request.getMileageDiscounts()).ifPresent(discounts ->
            result.stream().filter(p -> p.getCategory().equals(MileageFee))
                .forEach(p -> Optional.ofNullable(discounts.get(p.getStage()))
                    .ifPresent(mileageDiscount -> discountMileageFee(p, mileageDiscount)))
        );

        return result.stream().map(p -> new OrderPriceInfoResponse(p, skuMap)).collect(Collectors.toList());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void createOrderPriceInfos(@NonNull Orders order, YesChargingPoint point, List<MerchandiseReq> merchList) {
        List<OrderPriceInfo> priceInfos = calculateOrderPrice(order);
        if (point != null) {
            priceInfos.add(calculateYesCharging(order, point));
        }
        if (CollectionUtils.isNotEmpty(merchList)) {
            Set<String> skuCodes = merchList.stream().map(MerchandiseReq::getSkuCode).collect(Collectors.toSet());
            priceInfos.addAll(calculateMerchandisePrices(order, merchList, skuService.getMapByCodes(skuCodes)));
        }
        orderPriceInfoRepository.saveAll(priceInfos);
    }

    private List<OrderPriceInfo> calculateMerchandisePrices(Orders order, List<MerchandiseReq> merchList, Map<String, Sku> skuMap) {
        return merchList.stream()
            .map(merch -> calculateMerchandise(order, merch, skuMap.get(merch.getSkuCode())))
            .collect(Collectors.toList());
    }

    /**
     * 計算月費
     */
    public OrderPriceInfo calculateMonthlyFee(PriceInfo priceInfo, int month, Instant startDate, int stage) {
        Instant endDate = new Date(startDate.toEpochMilli()).toInstant().atZone(DateUtils.ZONE_TPE).plusMonths(month).minusDays(1).toInstant();
        int diffDays = (int) DateUtil.calculateDiffDate(DateUtil.convertToStartOfInstant(startDate), DateUtil.convertToStartOfInstant(endDate), DAYS);
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        PriceInfoDetail detail = new PriceInfoDetail();
        detail.setMonth(month);
        detail.setDay(diffDays);
        detail.setMonthlyFee(priceInfo.getUseMonthlyFee());

        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(MonthlyFee);
        if (stage == 1) {
            orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
        }
        orderPriceInfo.setLastPayDate(startDate);
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(priceInfo.getUseMonthlyFee() * month);
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    /**
     * 計算保險
     */
    public OrderPriceInfo calculateInsurance(PriceInfo priceInfo, boolean disclaimer, boolean premium, int month, int stage, Instant startDate) {
        Calendar endDate = Calendar.getInstance();
        endDate.setTimeInMillis(startDate.toEpochMilli());
        endDate.add(Calendar.MONTH, month);
        PriceInfoDetail detail = new PriceInfoDetail();
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(Insurance);
        detail.setMonth(month);
        detail.setDay((int) DateUtil.calculateDiffDate(startDate, Instant.ofEpochMilli(endDate.getTimeInMillis()), DAYS));

        if (stage == 1) {
            orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
        }
        orderPriceInfo.setLastPayDate(startDate);
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(0);
        if (disclaimer && premium) {
            orderPriceInfo.setAmount(priceInfo.getAllInsuranceFee());
            detail.setInsurance(priceInfo.getAllInsuranceFee());
        } else if (disclaimer) {
            orderPriceInfo.setAmount(priceInfo.getDisclaimerFee() * month);
            detail.setInsurance(priceInfo.getDisclaimerFee());
        } else if (premium) {
            orderPriceInfo.setAmount(priceInfo.getPremiumFee());
            detail.setInsurance(priceInfo.getPremiumFee());
        }
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    /**
     * 計算保險
     */
    public OrderPriceInfo calculateReplacement(PriceInfo priceInfo, boolean replacement, int month, int stage, Instant startDate) {
        Calendar endDate = Calendar.getInstance();
        endDate.setTimeInMillis(startDate.toEpochMilli());
        endDate.add(Calendar.MONTH, month);
        PriceInfoDetail detail = new PriceInfoDetail();
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(Replacement);
        detail.setMonth(month);
        detail.setDay((int) DateUtil.calculateDiffDate(startDate, Instant.ofEpochMilli(endDate.getTimeInMillis()), DAYS));

        if (stage == 1) {
            orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
        }
        orderPriceInfo.setLastPayDate(startDate);
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(0);
        if (replacement) {
            orderPriceInfo.setAmount(priceInfo.getReplacementCarFee() * month);
            detail.setReplacementCarFee(priceInfo.getReplacementCarFee());
        }
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    /**
     * 計算調度費用
     */
    public OrderPriceInfo calculateDispatch(PriceInfo priceInfo, String plateNo, String departStationCode, Instant startDate) {
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setStage(1);
        orderPriceInfo.setCategory(Dispatch);
        PriceInfoDetail priceInfoDetail = new PriceInfoDetail();
        priceInfoDetail.setReason(Dispatch.getDescriptionName());
        orderPriceInfo.setInfoDetail(priceInfoDetail);
        orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
        orderPriceInfo.setLastPayDate(DateUtil.convertToEndOfInstant(startDate));
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(calculateDispatchFee(priceInfo, plateNo, departStationCode));
        return orderPriceInfo;
    }

    /**
     * 計算保證金
     */
    public OrderPriceInfo calculateSecurityPosit(MainContract mainContract) {
        SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setStage(1);
        orderPriceInfo.setCategory(SecurityDeposit);
        orderPriceInfo.setLastPayDate(Instant.now().plus(1, DAYS));
        orderPriceInfo.setReceivableDate(Instant.now());
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(securityDepositInfo.getSecurityDeposit() - securityDepositInfo.getPaidSecurityDeposit());
        return orderPriceInfo;
    }

    /**
     * 預產生里程費資料
     * 2024-08-27後棄用 上線時間2024-10-31
     */
    @Deprecated
    private OrderPriceInfo initCalculateMileageFee(PriceInfo priceInfo, Orders order, int month, Instant startDate, int stage) {
        List<CalculateStage> stages = DateUtil.calculateStageAndDate(order);
        int stageCount = order.getMonth() < stages.size() * 3 ? stages.size() - 1 : stages.size();
        int renewFreeMileage = (!(order.getIsNewOrder() || month < 3) && stage == 1) ? 300 : 0;
        Calendar endDate = Calendar.getInstance();
        endDate.setTimeInMillis(startDate.toEpochMilli());
        endDate.add(Calendar.MONTH, month);
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        PriceInfoDetail detail = new PriceInfoDetail();
        if (priceInfo.getMileageDiscount() != null) {
            detail.setOriginalDiscountMileage(renewFreeMileage + (priceInfo.getMileageDiscount()
                .stream().filter(mdr -> mdr.getStage().equals(stageCount))
                .map(MileageDiscount::getDiscountMileage)
                .findFirst().orElse(0))
            );
            detail.setDiscountMileage(detail.getOriginalDiscountMileage());
        }
        detail.setMileageFee(priceInfo.getMileageFee() > 0 ? priceInfo.getMileageFee() : priceInfo.getOriginalMileageFee());
        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(MileageFee);
        orderPriceInfo.setLastPayDate(endDate.toInstant().compareTo(order.getExpectEndDate()) > 0 ? order.getExpectEndDate() : endDate.toInstant());
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(0);
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    /**
     * 預產生里程費資料
     */
    private OrderPriceInfo initCalculateMileageFeeV2(PriceInfo priceInfo, Orders order, int month, Instant startDate, int stage, SubscribeLevel level) {
        List<CalculateStage> stages = DateUtil.calculateStageAndDate(order);
        int renewFreeMileage = (!(order.getIsNewOrder() || month < 3) && stage == 1) ? PriceInfoConstant.renewFreeMileage : 0;
        Calendar endDate = Calendar.getInstance();
        endDate.setTimeInMillis(startDate.toEpochMilli());
        endDate.add(Calendar.MONTH, month);
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        PriceInfoDetail detail = new PriceInfoDetail();
        detail.setRenewDiscountMileage(renewFreeMileage);
        int stageCount = order.getMonth() < stages.size() * 3 ? stages.size() - 1 : stages.size();
        if (level.getMileageDiscount() != null) {
            detail.setRentalDiscountMileage((level.getMileageDiscount()
                .stream().filter(mdr -> mdr.getStage().equals(stageCount))
                .map(MileageDiscount::getDiscountMileage)
                .findFirst().orElse(0)) * month);
        }
        detail.setOriginalDiscountMileage(detail.getRenewDiscountMileage() + detail.getRentalDiscountMileage());
        detail.setMileageFee(priceInfo.getMileageFee() > 0 ? priceInfo.getMileageFee() : priceInfo.getOriginalMileageFee());
        detail.setDiscountMileage(detail.getOriginalDiscountMileage());
        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(MileageFee);
        orderPriceInfo.setLastPayDate(endDate.toInstant().compareTo(order.getExpectEndDate()) > 0 ? order.getExpectEndDate() : endDate.toInstant());
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(0);
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }


    public OrderPriceInfo calculateYesCharging(Orders order, YesChargingPoint point) {
        if (point == null) {
            return null;
        }
        OrderPriceInfo orderPriceInfo = initializeOrderPriceInfo(order, YesCharging, 1);
        PriceInfoDetail detail = new PriceInfoDetail();
        detail.setReason(point.getDescription());
        detail.setChargingPoint(point.getChargingPoint());
        orderPriceInfo.setAmount(point.getAmount());
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    private OrderPriceInfo initializeOrderPriceInfo(Orders order, PriceInfoDefinition.PriceInfoCategory category, int stage) {
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setOrderNo(order.getOrderNo());
        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(category);
        orderPriceInfo.setReceivableDate(Instant.now());
        orderPriceInfo.setLastPayDate(Optional.ofNullable(order.getEndDate()).orElseGet(order::getExpectEndDate));
        orderPriceInfo.setType(Pay.getCode());
        return orderPriceInfo;
    }

    private OrderPriceInfo calculateMerchandise(Orders order, MerchandiseReq merchandiseReq, Sku sku) {
        OrderPriceInfo orderPriceInfo = initializeOrderPriceInfo(order, Merchandise, Optional.ofNullable(merchandiseReq.getStage()).orElse(1));
        orderPriceInfo.setSkuCode(sku.getCode());
        Integer unitPrice = sku.getUnitPrice();
        int actualUnitPrice = Optional.ofNullable(merchandiseReq.getActualUnitPrice()).orElse(unitPrice);
        orderPriceInfo.setAmount(merchandiseReq.getQuantity() * actualUnitPrice);
        PriceInfoDetail detail = new PriceInfoDetail();
        detail.setUnitPrice(unitPrice);
        detail.setActualUnitPrice(actualUnitPrice);
        detail.setQuantity(merchandiseReq.getQuantity());
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    /**
     * 里程優惠
     */
    public List<OrderPriceInfo> discountMileageFee(Orders orders, Map<Integer, EmpMileageDiscount> discountMileages) {
        List<OrderPriceInfo> mileageFeeOrderPriceInfoList =
            orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(orders.getOrderNo())).build()).stream().filter(p -> p.getCategory().equals(MileageFee)).collect(Collectors.toList());
        for (OrderPriceInfo mileageFeeOrderPriceInfo : mileageFeeOrderPriceInfoList) {
            EmpMileageDiscount empMileageDiscount = discountMileages.get(mileageFeeOrderPriceInfo.getStage());
            if (empMileageDiscount != null) {
                discountMileageFee(mileageFeeOrderPriceInfo, empMileageDiscount);
            }
        }
        return mileageFeeOrderPriceInfoList;
    }

    /**
     * 里程優惠
     */
    public OrderPriceInfo discountMileageFee(OrderPriceInfo orderPriceInfo, EmpMileageDiscount empMileageDiscount) {
        if (orderPriceInfo.getReceivedAmount() != 0 || orderPriceInfo.isPaid()) {
            throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_HAVE_BEEN_PAYMENT);
        }
        if (orderPriceInfo.getCategory().equals(MileageFee)) {
            PriceInfoDetail detail = Optional.ofNullable(orderPriceInfo.getInfoDetail()).orElseGet(PriceInfoDetail::new);
            detail.setDiscountMileage(Optional.ofNullable(detail.getOriginalDiscountMileage()).orElse(0) + empMileageDiscount.getMileageDiscount());
            detail.setReason(empMileageDiscount.getReason());
        } else {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_MILEAGE_CATEGORY);
        }
        return orderPriceInfo;
    }


    /**
     * 訂單取消試算，計算取消訂單級距與全級距金額
     */
    public CancelOrderCalculateResponse cancelOrderCalculate(String orderNo) {
        Orders order = orderService.getOrder(orderNo);
        return PriceUtils.calculateCancelOrder(order, true);
    }

    /**
     * 官網設定里程費檢查
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void calculateMillageFeeValidate(String orderNo, int acctId, Integer orderPriceInfoId) {
        Orders orders = orderService.getUserOrder(orderNo, acctId);
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(orders);

        if (orderPriceInfoId == null) {
            List<OrderPriceInfo> orderPriceInfoList =
                getPriceInfosByOrder(orderNo, MileageFee, Pay).stream().filter(orderPriceInfo ->
                        !orderPriceInfo.isPaid() && orderPriceInfo.getReceivableDate().isBefore(Instant.now())
                            && !(orderPriceInfo.getStage().equals(calculateStageList.size())))
                    .collect(Collectors.toList());
            if (orderPriceInfoList.isEmpty()) {
                throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_UNPAID_NOT_FOUND);
            }
            orderPriceInfoList.forEach(opi -> calculateMillageFeeValidate(orderNo, acctId, opi.getId()));

        } else {
            OrderPriceInfo mileageOrderPriceInfo = get(orderPriceInfoId);
            // 已設定還車里程，無法再設定里程數
            if (orders.getReturnMileage() != null) {
                throw new SubscribeException(RETURN_MILEAGE_HAS_SETTING);
            }
            if (mileageOrderPriceInfo.getStage().equals(calculateStageList.size())) {
                throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_CAN_NOT_SETTING);
            }

        }
    }

    /**
     * 更新里程費
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo calculateMillageFee(String orderNo, int acctId, int currentMileage, Integer orderPriceInfoId) {
        OrderPriceInfo result;
        Orders orders = orderService.getUserOrder(orderNo, acctId);

        if (orderPriceInfoId == null) {
            List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(orders);

            List<OrderPriceInfo> orderPriceInfos = getPriceInfosByOrder(orderNo, MileageFee, Pay).stream()
                .filter(orderPriceInfo -> !orderPriceInfo.isPaid() && orderPriceInfo.getReceivableDate().isBefore(Instant.now())
                    && !(orderPriceInfo.getStage().equals(calculateStageList.size()))).collect(Collectors.toList());
            if (orderPriceInfos.isEmpty()) {
                throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_UNPAID_NOT_FOUND);
            }
            // 處理有里程優惠問題
            AtomicReference<Integer> preMileage = new AtomicReference<>(Optional.ofNullable(orders.getReportMileage()).orElse(orders.getDepartMileage()));

            orderPriceInfos.forEach(p -> Optional.ofNullable(p.getInfoDetail())
                .map(PriceInfoDetail::getStartMileage)
                .filter(startMileage -> startMileage < preMileage.get())
                .ifPresent(preMileage::set));
            if (preMileage.get() == 0) {
                throw new SubscribeException(ORDER_PRICE_INFO_LAST_END_MILEAGE_NOT_FOUND);
            }

            int count = 0;
            for (OrderPriceInfo orderPriceInfo : orderPriceInfos) {
                count++;
                int reportMileage = currentMileage;
                if (orderPriceInfos.size() != 1 && count < orderPriceInfos.size()) {
                    int discount = Optional.ofNullable(orderPriceInfo.getInfoDetail()).map(PriceInfoDetail::getDiscountMileage).orElse(0);
                    reportMileage = Math.min(preMileage.get() + discount, currentMileage);
                    preMileage.set(reportMileage);
                }
                calculateMillageFee(orderNo, acctId, reportMileage, orderPriceInfo.getId());
            }
            result = orderPriceInfos.get(orderPriceInfos.size() - 1);
        } else {
            OrderPriceInfo mileageFeeInfo = orderPriceInfoRepository.findById(orderPriceInfoId).orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
            result = calculateMillageFee(orderNo, acctId, currentMileage, mileageFeeInfo);
        }

        // 向 CRS 更新車輛最新里程數
        Cars car = carsService.findByPlateNo(orders.getPlateNo());
        crsService.updateKm(car, currentMileage, orders.getOrderNo());

        return result;
    }

    /**
     * 更新里程費
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo calculateMillageFee(String orderNo, int acctId, int currentMileage, OrderPriceInfo mileageFeeInfo) {
        Orders order = orderService.getUserOrder(orderNo, acctId);
        PriceInfo priceInfo = order.getContract().getMainContract().getOriginalPriceInfo();
        double mileageUnitFee = Optional.ofNullable(mileageFeeInfo).map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee).orElse(0.0);
        if (mileageUnitFee <= 0) {
            mileageUnitFee = priceInfo.getMileageFee() > 0 ? priceInfo.getMileageFee() : priceInfo.getOriginalMileageFee();
        }

        // 檢查是否已付款
        if (mileageFeeInfo.getReceivedAmount() > 0 || mileageFeeInfo.getPaymentId() != null) {
            throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_HAS_PAYED);
        }

        PriceInfoDetail detail = mileageFeeInfo.getInfoDetail();
        // 設定起始里程
        if (mileageFeeInfo.getStage() == 1) {
            detail.setStartMileage(order.getDepartMileage());
        } else {
            Integer startMileage = orderPriceInfoRepository.getPriceInfos(
                    OrderPriceInfoCriteria.builder()
                        .category(Collections.singletonList(MileageFee))
                        .type(Pay.getCode())
                        .orderNo(Collections.singletonList(order.getOrderNo()))
                        .stage(mileageFeeInfo.getStage() - 1).build()
                ).stream().findAny().map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getEndMileage)
                .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_LAST_END_MILEAGE_NOT_FOUND));
            detail.setStartMileage(startMileage);
        }
        if (detail.getStartMileage() > currentMileage) {
            throw new SubscribeException(ORDER_PRICE_INFO_LAST_END_MILEAGE_OVER_CURRENT);
        }
        detail.setEndMileage(currentMileage);
        detail.setMileageFee(mileageUnitFee);

        int amount = calculateAmountAndUpdateMileageDetail(detail);
        mileageFeeInfo.setAmount(amount);

        mileageFeeInfo.setType(Pay.getCode());

        order.setReportMileage(currentMileage);

        // 設定下期里程費起始里程
        OrderPriceInfo nextStageMileageInfo = getPriceInfosByOrder(order.getOrderNo(), OrderPriceInfoCriteriaRequest.builder().stage(mileageFeeInfo.getStage() + 1).category(Collections.singletonList(MileageFee)).type(Pay.getCode()).build()).stream()
            .filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0).findFirst().orElse(null);
        if (nextStageMileageInfo != null) {
            nextStageMileageInfo.getInfoDetail().setStartMileage(currentMileage);
        }
        orderService.updateOrder(order);

        return orderPriceInfoRepository.save(mileageFeeInfo);
    }

    public int calculateAmountAndUpdateMileageDetail(PriceInfoDetail detail) {
        // 檢查起始與結束里程
        if (detail.getEndMileage() < detail.getStartMileage()) {
            throw new SubscribeException(UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE);
        }

        // 計算折扣後里程與總里程
        Integer discountMileage = Optional.ofNullable(detail.getDiscountMileage()).orElse(0);
        int totalMileage = Math.max(detail.getEndMileage() - detail.getStartMileage() - discountMileage, 0);
        detail.setTotalMileage(totalMileage);

        // 計算並回傳里程費金額
        return (int) Math.round(detail.getMileageFee() * totalMileage);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void rePayMileageFee(Orders order, boolean returnEarly, List<OrderPriceInfo> orderPriceInfosToDelete) {
        boolean isRePayMileageFee = getPriceInfosByOrder(order.getOrderNo(), MonthlyFee, Pay).stream().anyMatch(opi -> !opi.isPaid());
        OrderPriceInfo addMileageFee = getPriceInfosByOrder(order.getOrderNo(), Others, Pay).stream()
            .filter(opi -> {
                PriceInfoDetail detail = opi.getInfoDetail();
                return detail != null && detail.getAddMileage() != null;
            })
            .findFirst()
            .orElse(null);
        if (addMileageFee != null && addMileageFee.isPaid()) {
            return;
        }
        if (isRePayMileageFee) {
            List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrder(order.getOrderNo(), MileageFee, Pay);
            AtomicInteger reAddMileage = new AtomicInteger();
            double mileageFee = orderPriceInfoList.stream().findAny().map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee).orElse(0.0);

            orderPriceInfoList.forEach(opi -> {
                Optional.ofNullable(opi).map(OrderPriceInfo::getInfoDetail).ifPresent(detail -> {
                    // 計算提前還車優惠回收里程
                    int saveRealMileage = detail.getSaveRealMileage();
                    int discountMileage = Optional.ofNullable(detail.getDiscountMileage()).orElse(0);
                    int rentalDiscountMileage = Optional.ofNullable(detail.getRentalDiscountMileage()).orElse(0);

                    int recoverMileage = saveRealMileage - (discountMileage - rentalDiscountMileage);
                    recoverMileage = Math.max(recoverMileage, 0);

                    reAddMileage.addAndGet(recoverMileage);
                });
            });
            if (addMileageFee == null) {
                PriceInfoDetail detail = new PriceInfoDetail();
                detail.setAddMileage(reAddMileage.get());
                detail.setMileageFee(mileageFee);
                addMileageFee = new OrderPriceInfo();
                addMileageFee.setOrderNo(order.getOrderNo());
                addMileageFee.setCategory(Others);
                addMileageFee.setReceivableDate(Instant.now());
                addMileageFee.setLastPayDate(order.getExpectEndDate());
                addMileageFee.setType(Pay.getCode());
                addMileageFee.setAmount(0);
                addMileageFee.setInfoDetail(detail);
                addMileageFee.setStage(DateUtil.calculateStageAndDate(order).size());
                addMileageFee.setAmount(Long.valueOf(Math.round(mileageFee * reAddMileage.get())).intValue());
            }

            if (reAddMileage.get() > 0) {
                PriceInfoDetail detail = Optional.ofNullable(addMileageFee.getInfoDetail()).orElse(new PriceInfoDetail());
                detail.setAddMileage(reAddMileage.get());
                detail.setMileageFee(mileageFee);
                detail.setReason(String.format("回收里程優惠 %d km ( %d x %.2f )", reAddMileage.get(), reAddMileage.get(), mileageFee));
                addMileageFee.setInfoDetail(detail);
                addMileageFee.setAmount(Long.valueOf(Math.round(mileageFee * reAddMileage.get())).intValue());
                orderPriceInfoRepository.save(addMileageFee);

                if (returnEarly) {
                    orderPriceInfosToDelete.add(addMileageFee);
                }
            } else {
                if (addMileageFee.getId() != null) {
                    orderPriceInfoRepository.delete(addMileageFee);
                }
            }
        } else {
            if (addMileageFee != null) {
                orderPriceInfoRepository.delete(addMileageFee);
            }
        }
    }

    /**
     * 月費減項
     */
    public OrderPriceInfo calculateDiscountMonthlyFee(int month, int monthDiscountUnit, Instant startDate, int stage) {
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        PriceInfoDetail detail = new PriceInfoDetail();
        detail.setMonth(month);

        orderPriceInfo.setStage(stage);
        orderPriceInfo.setCategory(MonthlyFee);
        if (stage == 1) {
            orderPriceInfo.setReceivableDate(Instant.now());
        }
        orderPriceInfo.setLastPayDate(startDate);
        orderPriceInfo.setType(Discount.getCode());
        orderPriceInfo.setAmount(month * monthDiscountUnit * 1000);
        orderPriceInfo.setInfoDetail(detail);
        return orderPriceInfo;
    }

    /**
     * 計算調度費用
     */
    private int calculateDispatchFee(PriceInfo priceInfo, String plateNo, String departStationCode) {
        Cars car = carsService.findByPlateNo(plateNo);
        if (Objects.isNull(car)) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        if (StringUtils.isNotBlank(car.getLocationStationCode()) && !car.getLocationStationCode().equals(departStationCode)) {
            Stations carStation = stationService.findByStationCode(car.getLocationStationCode());
            Stations departStation = stationService.findByStationCode(departStationCode);
            if (!Objects.equals(GeoDefine.GeoRegion.enumToCode(GeoDefine.GeoRegion.valueOf(carStation.getLocateGeoRegion())), GeoDefine.GeoRegion.enumToCode(GeoDefine.GeoRegion.valueOf(departStation.getLocateGeoRegion())))) {
                return priceInfo.getDispatchFee();
            }
        }
        return 0;
    }

    /**
     * 增加額外費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setExtraFee(String orderNo, ExtraFeeRequest request, String memberId, boolean isAccident) {
        List<String> addableCategoryNames = getCategoriesAddableAsExtraFee().stream()
            .map(PriceInfoDefinition.PriceInfoCategory::getDescriptionName)
            .collect(Collectors.toList());
        List<ExtraFeeRequest.ExtraFee> fees = request.getExtraFeeList();
        for (ExtraFeeRequest.ExtraFee fee : fees) {
            if (!(canAddAsExtraFee(fee.getCategory()) || CarAccident.equals(fee.getCategory()))) {
                throw new BadRequestException(String.format("設定項目不在額外費用項目支援範圍(%s)或事故還車(%s)",
                    String.join(",", addableCategoryNames), CarAccident.getDescriptionName()));
            }
        }
        boolean hasCarAccident = fees.stream().anyMatch(fee -> fee.getCategory().equals(CarAccident));

        List<OrderPriceInfo> priceInfos = getPriceInfosByOrder(orderNo);
        Map<Integer, OrderPriceInfo> feesMap = priceInfos.stream().collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));

        Orders orders = orderService.getOrder(orderNo);
        List<CalculateStage> stages = DateUtil.calculateStageAndDate(orders);
        CalculateStage currentStage = DateUtil.calculateStageAndDateByTargetDate(orders, Instant.now());

        for (ExtraFeeRequest.ExtraFee fee : fees) {
            if (fee.getPriceInfoId() != null) {
                OrderPriceInfo opi = feesMap.get(fee.getPriceInfoId());
                if (opi == null) {
                    throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND.getHttpStatus(), ORDER_PRICE_INFO_NOT_FOUND,
                        ORDER_PRICE_INFO_NOT_FOUND.getMsg() + ", id: " + fee.getPriceInfoId() + ", category: " + fee.getCategory());
                }
                if (opi.isPaid() && (!Objects.equals(fee.getAmount(), opi.getAmount()) || fee.isDelete())) {
                    throw new SubscribeException(ORDER_PRICE_INFO_HAVE_PAID.getHttpStatus(), ORDER_PRICE_INFO_HAVE_PAID,
                        ORDER_PRICE_INFO_HAVE_PAID.getMsg() + ", id: " + fee.getPriceInfoId() + ", category: " + fee.getCategory());
                }

                // 刪除費用
                if (fee.isDelete()) {
                    orderPriceInfoRepository.deleteById(fee.getPriceInfoId());
                } else {
                    // 更新費用
                    if (CarAccident.equals(fee.getCategory())) {
                        if (fee.getStage() < currentStage.getStage()) {
                            throw new SubscribeException(ORDER_PRICE_INFO_EXTRA_FEE_NOT_BEFORE_THAN_CURRENT);
                        }
                        PriceInfoMapper.buildUpdatedCarAccidentOPI(request, fee, orders, opi, memberId);
                    }
                    if (canAddAsExtraFee(fee.getCategory())) {
                        PriceInfoMapper.buildUpdatedExtraFeeOPI(fee, opi, memberId);
                    }
                    orderPriceInfoRepository.saveAndFlush(opi);
                }
            } else {

                if (fee.getStage() < currentStage.getStage()) {
                    throw new SubscribeException(ORDER_PRICE_INFO_EXTRA_FEE_NOT_BEFORE_THAN_CURRENT);
                }

                // 新增費用
                CalculateStage targetStage = stages.get(fee.getStage() - 1);
                OrderPriceInfo opi = new OrderPriceInfo();
                if (fee.getStage() == 1 || OrderStatus.ARRIVE_NO_CLOSE.getStatus() == orders.getStatus() || orders.getEndDate() != null) {
                    opi.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
                } else {
                    opi.setReceivableDate(DateUtil.convertToStartOfInstant(targetStage.getStartDate().minus(10, DAYS)));
                }

                // source: 後台事故還車 / TaskService && Mobile
                if (isAccident && hasCarAccident && CarAccident.equals(fee.getCategory())) {
                    PriceInfoMapper.buildNewCarAccidentOPI(fee, orders, targetStage, opi, memberId);
                }

                if (canAddAsExtraFee(fee.getCategory())) {
                    PriceInfoMapper.buildNewExtraFeeOPI(fee, orders, targetStage, opi, memberId);
                }

                orderPriceInfoRepository.saveAndFlush(opi);
                orderService.updateOrder(orders);
            }
        }

        //  source: 後台事故還車 / TaskService && Mobile，
        //  不管車損費用新增/刪除/更新與否，皆依照Request要求更新車損、議價狀態
        if (isAccident) {
            if (orders.getAccidentInfo() != null) {
                orders.getAccidentInfo().setReturnNego(request.getReturnNego());
                orders.getAccidentInfo().setCarDamaged(request.isCarDamaged());
                orderService.updateOrder(orders);
            } else {
                AccidentInfo info = PriceInfoMapper.toCarAccidentTypeInfo(request, memberId);
                orders.setAccidentInfo(info);
            }
        }
        orderService.checkIsUnpaid(orders);
    }

    /**
     * 設定汽車用品費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setMerchandiseFee(String orderNo, List<MerchandiseInfoRequest.MerchandiseInfo> merchandiseList, String memberId) {
        Map<Integer, OrderPriceInfo> map = getPriceInfosByOrder(orderNo).stream().collect(Collectors.toMap(OrderPriceInfo::getId, p -> p));
        Orders orders = orderService.getOrder(orderNo);
        List<CalculateStage> stages = DateUtil.calculateStageAndDate(orders);
        CalculateStage currentStage = DateUtil.calculateStageAndDateByTargetDate(orders, Instant.now());
        Map<String, Sku> skuMap = skuService.getMapByCodes(merchandiseList.stream().map(MerchandiseInfoRequest.MerchandiseInfo::getSkuCode).collect(Collectors.toSet()));
        for (MerchandiseInfoRequest.MerchandiseInfo merchandiseInfo : merchandiseList) {
            if (merchandiseInfo.getPriceInfoId() != null) {
                OrderPriceInfo orderPriceInfo = map.get(merchandiseInfo.getPriceInfoId());
                if (orderPriceInfo == null) {
                    throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
                }
                if (orderPriceInfo.isPaid()
                    && (merchandiseInfo.isDelete()
                    || !Objects.equals(merchandiseInfo.getActualUnitPrice(), orderPriceInfo.getInfoDetail().getActualUnitPrice())
                    || !Objects.equals(merchandiseInfo.getQuantity(), orderPriceInfo.getInfoDetail().getQuantity()))) {
                    throw new SubscribeException(ORDER_PRICE_INFO_HAVE_PAID);
                }
                if (merchandiseInfo.isDelete()) {
                    // 刪除
                    orderPriceInfoRepository.deleteById(merchandiseInfo.getPriceInfoId());
                } else {
                    // 更新明細
                    PriceInfoDetail detail = orderPriceInfo.getInfoDetail();
                    detail.setActualUnitPrice(merchandiseInfo.getActualUnitPrice());
                    detail.setQuantity(merchandiseInfo.getQuantity());
                    orderPriceInfo.setAmount(merchandiseInfo.getActualUnitPrice() * merchandiseInfo.getQuantity());
                    orderPriceInfoRepository.saveAndFlush(orderPriceInfo);
                }
            } else {
                CalculateStage targetStage = stages.get(merchandiseInfo.getStage() - 1);
                // 新增
                OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
                PriceInfoDetail detail = new PriceInfoDetail();
                detail.setUnitPrice(skuMap.get(merchandiseInfo.getSkuCode()).getUnitPrice());
                detail.setActualUnitPrice(merchandiseInfo.getActualUnitPrice());
                detail.setQuantity(merchandiseInfo.getQuantity());
                detail.setAdminId(memberId);
                detail.setDay(targetStage.getDay());
                detail.setMonth(targetStage.getMonth());
                if (merchandiseInfo.getStage() < currentStage.getStage()) {
                    throw new SubscribeException(ORDER_PRICE_INFO_EXTRA_FEE_NOT_BEFORE_THAN_CURRENT);
                }
                orderPriceInfo.setStage(merchandiseInfo.getStage());
                if (merchandiseInfo.getStage() == 1 || OrderStatus.ARRIVE_NO_CLOSE.getStatus() == orders.getStatus()) {
                    orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
                } else {
                    orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(targetStage.getStartDate().minus(10, DAYS)));
                }
                orderPriceInfo.setLastPayDate(DateUtil.convertToEndOfInstant(targetStage.getEndDate()));
                orderPriceInfo.setInfoDetail(detail);
                orderPriceInfo.setCategory(Merchandise);
                orderPriceInfo.setType(Pay.getCode());
                orderPriceInfo.setAmount(merchandiseInfo.getActualUnitPrice() * merchandiseInfo.getQuantity());
                orderPriceInfo.setOrderNo(orderNo);
                orderPriceInfo.setSkuCode(merchandiseInfo.getSkuCode());
                orderPriceInfoRepository.saveAndFlush(orderPriceInfo);
            }
        }
    }

    @Transactional
    public void updateOrderPriceInfo(OrderPriceInfoUpdateRequest updateRequest) {
        updateRequest.validate();
        OrderPriceInfo opi = get(updateRequest.getId());
        if (opi == null) {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        if (opi.isPaid()) {
            throw new SubscribeException(ORDER_PRICE_INFO_HAVE_PAID);
        }
        opi.setReceivableDate(updateRequest.getReceivableDate());
        opi.setLastPayDate(updateRequest.getLastPayDate());
        orderPriceInfoRepository.save(opi);
    }

    /**
     * 計算提前還車應退費用
     */
    public int calculateReturnEarlyRefundFee(Orders order) {
        CalculateStage calculateStage = DateUtil.calculateStageAndDateByTargetDate(order, order.getEndDate());
        // 提前天數
        long diffDays = DateUtil.calculateDiffDate(order.getEndDate(), calculateStage.getEndDate(), DAYS);
        OrderPriceInfo monthlyOrderPriceInfo = orderPriceInfoRepository.getPriceInfos(
            OrderPriceInfoCriteria.builder()
                .orderNo(Collections.singletonList(order.getOrderNo()))
                .category(Collections.singletonList(MonthlyFee))
                .type(Pay.getCode())
                .stage(calculateStage.getStage())
                .build()).stream().findAny().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        return monthlyOrderPriceInfo.getAmount() / monthlyOrderPriceInfo.getInfoDetail().getDay() * (int) diffDays;
    }

    /**
     * 設定提早還車款項
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setReturnEarly(Orders order) {
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(order);
        CalculateStage calculateStage = DateUtil.calculateStageAndDateByTargetDate(order, order.getEndDate());

        // 提前天數
        long diffDays = DateUtil.calculateDiffDate(order.getEndDate(), calculateStage.getEndDate(), DAYS);
        String orderNo = order.getOrderNo();
        OrderPriceInfo monthlyOrderPriceInfo = orderPriceInfoRepository.getPriceInfos(
            OrderPriceInfoCriteria.builder()
                .orderNo(Collections.singletonList(orderNo))
                .category(Collections.singletonList(MonthlyFee))
                .type(Pay.getCode())
                .stage(calculateStage.getStage())
                .build()).stream().findAny().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));

        // 負向未繳費用明細
        OrderPriceInfo monthlyNegativeOrderPriceInfo = getPriceInfosByOrder(orderNo, ReturnEarly, null)
            .stream().filter(orderPriceInfo -> (orderPriceInfo.getType() == Refund.getCode() || orderPriceInfo.getType() == Discount.getCode())
                && !orderPriceInfo.isPaid()
            ).findAny().orElse(null);

        if (monthlyNegativeOrderPriceInfo == null) {
            monthlyNegativeOrderPriceInfo = new OrderPriceInfo();
            monthlyNegativeOrderPriceInfo.setOrderNo(orderNo);
            monthlyNegativeOrderPriceInfo.setLastPayDate(order.getEndDate().plus(1, DAYS));
            monthlyNegativeOrderPriceInfo.setReceivableDate(Instant.now());
            monthlyNegativeOrderPriceInfo.setCategory(ReturnEarly);


            // 是否付過月費款項
            //      是:檢查是否是最後一筆月費
            //              是:退款
            //              否:再下一期月費做折扣
            //      否:在該期月費產生折扣
            if (monthlyOrderPriceInfo.getPaymentId() != null) {
                if (calculateStageList.size() == calculateStage.getStage()) {
                    monthlyNegativeOrderPriceInfo.setType(Refund.getCode());
                    monthlyNegativeOrderPriceInfo.setRefPriceInfoNo(monthlyOrderPriceInfo.getId());
                    monthlyNegativeOrderPriceInfo.setRecTradeId(monthlyOrderPriceInfo.getRecTradeId());
                } else {
                    OrderPriceInfo nextStageMonthlyOrderPriceInfo = orderPriceInfoRepository.getPriceInfos(
                        OrderPriceInfoCriteria.builder()
                            .orderNo(Collections.singletonList(orderNo))
                            .category(Collections.singletonList(MonthlyFee))
                            .type(Pay.getCode())
                            .stage(calculateStage.getStage() + 1)
                            .build()).stream().findAny().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
                    monthlyNegativeOrderPriceInfo.setType(Discount.getCode());
                    monthlyNegativeOrderPriceInfo.setRefPriceInfoNo(nextStageMonthlyOrderPriceInfo.getId());
                }
            } else {
                monthlyNegativeOrderPriceInfo.setType(Refund.getCode());
                monthlyNegativeOrderPriceInfo.setRefPriceInfoNo(monthlyOrderPriceInfo.getId());
                monthlyNegativeOrderPriceInfo.setRecTradeId(monthlyOrderPriceInfo.getRecTradeId());
            }
        }
        PriceInfoDetail detail = Optional.ofNullable(monthlyNegativeOrderPriceInfo.getInfoDetail()).orElse(new PriceInfoDetail());
        detail.setDay((int) diffDays);
        monthlyNegativeOrderPriceInfo.setInfoDetail(detail);
        monthlyNegativeOrderPriceInfo.setAmount(monthlyOrderPriceInfo.getAmount() / monthlyOrderPriceInfo.getInfoDetail().getDay() * (int) diffDays);

        orderPriceInfoRepository.save(monthlyNegativeOrderPriceInfo);

        if (calculateStageList.size() != calculateStage.getStage()) {
            List<OrderPriceInfo> monthlyNotPaid = getPriceInfosByOrder(orderNo, MonthlyFee, Pay);
            monthlyNotPaid.forEach(orderPriceInfo -> orderPriceInfo.setReceivableDate(Instant.now()));
            orderPriceInfoRepository.saveAll(monthlyNotPaid);
        }
    }

    /**
     * 設定提早還車款項，需繳納後面期數月費餘額
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setReturnEarlyV2(Orders order) {
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(order);
        CalculateStage calculateStage = DateUtil.calculateStageAndDateByTargetDate(order, order.getEndDate());

        if (calculateStageList.size() != calculateStage.getStage()) {
            List<OrderPriceInfo> monthlyNotPaid = getPriceInfosByOrder(order.getOrderNo(), MonthlyFee, Pay)
                .stream().filter(orderPriceInfo -> !orderPriceInfo.isPaid()).collect(Collectors.toList());
            monthlyNotPaid.addAll(getPriceInfosByOrder(order.getOrderNo(), Insurance, Pay)
                .stream().filter(orderPriceInfo -> !orderPriceInfo.isPaid()).collect(Collectors.toList()));
            // 提前還車不給月費折扣
            monthlyNotPaid.addAll(getPriceInfosByOrder(order.getOrderNo(), MonthlyFee, Discount)
                .stream().filter(orderPriceInfo -> !orderPriceInfo.isPaid()).peek(orderPriceInfo -> orderPriceInfo.setAmount(0)).collect(Collectors.toList()));
            monthlyNotPaid.forEach(orderPriceInfo -> orderPriceInfo.setReceivableDate(Instant.now()));
            orderPriceInfoRepository.saveAll(monthlyNotPaid);
        }
    }

    /**
     * 提前還車退款
     *
     * @Deprecated 2023/12/11 棄用，提前還車不退月費，依樣先折扣應付後再進行退款，故該功能棄用[CYB-10187]
     */
    @Deprecated
    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo refundReturnEarlyPriceInfo(Orders order, CarDropOffDiscountRequest carDropOffDiscountRequest) {
        // 拿取最後一次付款月費
        OrderPriceInfo monthlyPaid = getPriceInfosByOrder(order.getOrderNo(), MonthlyFee, Pay)
            .stream().filter(orderPriceInfo -> orderPriceInfo.isPaid() && orderPriceInfo.getReceivedAmount() > 0).max(Comparator.comparing(OrderPriceInfo::getStage)).orElse(null);
        List<OrderPriceInfo> refundEarlyReturnList = getPriceInfosByOrder(order.getOrderNo(), ReturnEarly, Refund);
        // 是否曾經已退款，基本上只有還車後才退款，但該功能只有還車前才可設定，故應該不會走到該邏輯
        if (refundEarlyReturnList.stream().anyMatch(orderPriceInfo -> orderPriceInfo.getReceivedAmount() > 0 || orderPriceInfo.getPaymentId() != null)) {
            throw new SubscribeException(ALREADY_REFUND);
        }
        List<OrderPriceInfo> refundList = refundEarlyReturnList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0 && !orderPriceInfo.isPaid()).collect(Collectors.toList());
        orderPriceInfoRepository.deleteAll(refundList);
        return generateNegativeOrderPriceInfo(monthlyPaid, carDropOffDiscountRequest.getDiscount(), ReturnEarly);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo refundCancelOrderPriceInfo(Orders order, CarDropOffDiscountRequest carDropOffDiscountRequest) {
        // 拿取保證金
        OrderPriceInfo security = getPriceInfosByOrder(order.getOrderNo(), SecurityDeposit, Pay)
            .stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() > 0).min(Comparator.comparing(OrderPriceInfo::getStage))
            .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));
        List<OrderPriceInfo> cancelOrderPriceInfos = getPriceInfosByOrder(order.getOrderNo(), SecurityDeposit, Refund);
        // 是否曾經已退款，基本上只有還車後才退款，但該功能只有還車前才可設定，故應該不會走到該邏輯
        if (cancelOrderPriceInfos.stream().anyMatch(orderPriceInfo -> orderPriceInfo.getReceivedAmount() > 0 || orderPriceInfo.getPaymentId() != null)) {
            throw new SubscribeException(ALREADY_REFUND);
        }
        List<OrderPriceInfo> refundList = cancelOrderPriceInfos.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0 && !orderPriceInfo.isPaid()).collect(Collectors.toList());
        orderPriceInfoRepository.deleteAll(refundList);
        return generateNegativeOrderPriceInfo(security, carDropOffDiscountRequest.getDiscount(), CancelBooking);
    }

    /**
     * 設定延後還車
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setReturnLate(Orders order) {
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(order);
        long diffDays = DateUtil.calculateDiffDate(order.getExpectEndDate(), order.getEndDate(), DAYS);
        int insuranceFee = Optional.ofNullable(order.getContract()).map(Contract::getDisclaimer).map(disClaimer -> disClaimer ? order.getContract().getMainContract().getOriginalPriceInfo().getDisclaimerFee() : 0).orElse(0);
        int replaceFee = Optional.ofNullable(order.getContract()).map(Contract::getReplacement).map(replace -> replace ? order.getContract().getMainContract().getOriginalPriceInfo().getReplacementCarFee() : 0).orElse(0);
        int amt = Double.valueOf((order.getContract().getMainContract().getOriginalPriceInfo().getUseMonthlyFee() + insuranceFee + replaceFee) * 0.2 * (int) diffDays).intValue();

        List<OrderPriceInfo> positiveList = new ArrayList<>(orderPriceInfoRepository.getPriceInfos(
            OrderPriceInfoCriteria.builder()
                .orderNo(Collections.singletonList(order.getOrderNo()))
                .category(Collections.singletonList(ReturnLate))
                .build()));
        AtomicInteger paid = new AtomicInteger();
        List<OrderPriceInfo> paidList = positiveList.stream().filter(info -> info.getReceivedAmount() > 0 || info.getPaymentId() != null).peek(p -> paid.addAndGet(p.getReceivedAmount())).collect(Collectors.toList());
        if (!paidList.isEmpty()) {
            throw new SubscribeException(ORDER_PRICE_INFO_RETURN_LATE_HAVE_PAID);
        }


        OrderPriceInfo returnLatePriceInfo = getPriceInfosByOrder(order.getOrderNo(), ReturnLate, null)
            .stream().filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode() && orderPriceInfo.getPaymentId() == null
            ).findAny().orElse(null);
        if (returnLatePriceInfo == null) {
            returnLatePriceInfo = new OrderPriceInfo();
            returnLatePriceInfo.setOrderNo(order.getOrderNo());
            returnLatePriceInfo.setLastPayDate(order.getEndDate().plus(1, DAYS));
            returnLatePriceInfo.setReceivableDate(Instant.now());
            returnLatePriceInfo.setCategory(ReturnLate);
            returnLatePriceInfo.setStage(calculateStageList.size());
            returnLatePriceInfo.setType(Pay.getCode());
        }
        PriceInfoDetail detail = Optional.ofNullable(returnLatePriceInfo.getInfoDetail()).orElse(new PriceInfoDetail());
        detail.setDay((int) diffDays);
        detail.setDelayDays((int) diffDays);
        detail.setMonthlyFee(order.getContract().getMainContract().getOriginalPriceInfo().getUseMonthlyFee());
        detail.setInsurance(insuranceFee);
        returnLatePriceInfo.setAmount(amt);
        returnLatePriceInfo.setInfoDetail(detail);
        orderPriceInfoRepository.save(returnLatePriceInfo);
    }

    /**
     * 產生Etag的OrderPriceInfo
     */
    public OrderPriceInfo generateEtagOrderPriceInfo(ETagInfo tagInfo, Orders order) {
        CalculateStage calculateStage = DateUtil.calculateStageAndDateByTargetDate(order, Optional.ofNullable(tagInfo.getReturnDate()).orElse(tagInfo.getDepartDate()));
        OrderPriceInfo orderPriceInfo = new OrderPriceInfo();
        orderPriceInfo.setOrderNo(order.getOrderNo());
        orderPriceInfo.setLastPayDate(getLastPayDate(order));
        orderPriceInfo.setReceivableDate(Instant.now());
        orderPriceInfo.setStage(calculateStage.getStage());
        orderPriceInfo.setCategory(ETag);
        orderPriceInfo.setType(Pay.getCode());
        orderPriceInfo.setAmount(Optional.ofNullable(tagInfo.getETagAmt()).orElse(0));
        return orderPriceInfo;
    }

    /**
     * 取得訂單費用資訊
     */
    public List<OrderPriceInfo> getPriceInfosByOrderAndAcctId(String orderNo, int acctId) {
        // 確認是否為使用者訂單
        orderService.getUserOrder(orderNo, acctId);
        return getPriceInfosByOrder(orderNo);
    }

    /**
     * 條件式取得訂單費用資訊
     */
    public Page<OrderPriceInfoWithDescription> searchPageable(OrderPriceInfoCriteriaRequest request) {
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        BeanUtils.copyProperties(request, criteria);
        long total = orderPriceInfoRepository.countNonZeroAmountPriceInfos(criteria);
        if (total == 0) {
            return Page.of(0, Collections.emptyList(), request.getSkip(), request.getLimit());
        }

        // 取得所有符合條件且金額不為0的資料
        List<OrderPriceInfoWithDescription> priceInfos = getNonZeroAmountPriceInfos(criteria, request.getSkip(), request.getLimit());

        return Page.of(total, priceInfos, request.getSkip(), request.getLimit());
    }

    private List<OrderPriceInfoWithDescription> getNonZeroAmountPriceInfos(OrderPriceInfoCriteria criteria, Integer skip, Integer limit) {
        List<PriceInfoDefinition.PriceInfoCategory> categories = criteria.getCategory();
        Map<String, Sku> skuMap = categories != null && categories.contains(Merchandise) ? skuService.getAllSkus() : null;
        return orderPriceInfoRepository.getPriceInfos(criteria, skip, limit)
            .stream()
            .filter(priceInfo -> priceInfo.getAmount() != 0)
            .map(priceInfo -> new OrderPriceInfoWithDescription(priceInfo, skuMap))
            .collect(Collectors.toList());
    }

    /**
     * 取得訂單費用資訊
     */
    public List<OrderPriceInfo> getPriceInfosByOrder(String orderNo) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(orderNo)).build());
    }


    /**
     * 條件式取得訂單費用資訊
     */
    public List<OrderPriceInfo> getPriceInfosByOrder(String orderNo, OrderPriceInfoCriteriaRequest request) {
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        BeanUtils.copyProperties(request, criteria);
        criteria.setOrderNo(Collections.singletonList(orderNo));
        return orderPriceInfoRepository.getPriceInfos(criteria);
    }

    public List<OrderPriceInfo> getPriceInfosByOrder(String orderNo, PriceInfoDefinition.PriceInfoCategory category, PriceInfoDefinition.PriceInfoType type) {
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        criteria.setOrderNo(Collections.singletonList(orderNo));
        if (category != null) {
            criteria.setCategory(Collections.singletonList(category));
        }
        if (type != null) {
            criteria.setType(type.getCode());
        }
        return orderPriceInfoRepository.getPriceInfos(criteria);
    }

    public List<OrderPriceInfo> getPriceInfosByOrders(List<String> orderNos) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(orderNos).build());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<OrderPriceInfo> getPriceInfosByOrderWithNewTransaction(String orderNo) {
        return getPriceInfosByOrder(orderNo);
    }

    public Map<Integer, StageOrderPriceInfoResponse> getUserStageGroupPriceInfoByOrder(String orderNo, OrderPriceInfoCriteriaRequest request, Integer acctId) {
        Orders orders = orderService.getUserOrder(orderNo, acctId);
        return getIntegerStageOrderPriceInfoResponseMap(request, orders);
    }


    public Map<Integer, StageOrderPriceInfoResponse> getStageGroupPriceInfoByOrder(String orderNo, OrderPriceInfoCriteriaRequest request) {
        Orders orders = orderService.getOrder(orderNo);
        return getIntegerStageOrderPriceInfoResponseMap(request, orders);
    }

    private Map<Integer, StageOrderPriceInfoResponse> getIntegerStageOrderPriceInfoResponseMap(OrderPriceInfoCriteriaRequest request, Orders orders) {
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(orders);
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        BeanUtils.copyProperties(request, criteria);
        criteria.setOrderNo(Collections.singletonList(orders.getOrderNo()));
        List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoRepository.getPriceInfos(criteria);

        OrderPriceInfo securityOrderPrice = orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getCategory().equals(SecurityDeposit) && orderPriceInfo.getType() == Pay.getCode())
            .findAny().orElse(null);
        Map<Integer, ETagInfo> eTagInfoMap = eTagService.getByOrderPriceInfoIdIn(orderPriceInfoList.stream().map(OrderPriceInfo::getId).collect(Collectors.toList()))
            .stream().collect(Collectors.toMap(ETagInfo::getOrderPriceInfoId, Function.identity()));
        Map<String, Sku> allSkus = skuService.getAllSkus();

        Comparator<OrderPriceInfoResponse> comparator = Comparator.comparing(OrderPriceInfoResponse::getCategory, Comparator.comparing(PriceInfoDefinition.PriceInfoCategory::getSortIndex)).thenComparing(OrderPriceInfoResponse::getType);

        return orderPriceInfoList.stream()
            .filter(orderPriceInfo -> (request.isSecurityDeposit() || !orderPriceInfo.getCategory().equals(SecurityDeposit))
                && !(securityOrderPrice != null && orderPriceInfo.getCategory().equals(CancelBooking) && Objects.equals(orderPriceInfo.getRefPriceInfoNo(), securityOrderPrice.getId())))
            .map(orderPriceInfo -> new OrderPriceInfoResponse(orderPriceInfo, eTagInfoMap.get(orderPriceInfo.getId()), calculateStageList.get(orderPriceInfo.getStage() - 1), allSkus))
            .filter(opi -> request.isCredit() || StringUtils.isBlank(opi.getUid())
                || (StringUtils.isNotBlank(opi.getUid()) && Optional.ofNullable(opi.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(false)))
            .peek(orderPriceInfoResponse -> {
                if (orderPriceInfoResponse.getCategory().equals(SecurityDeposit)) {
                    SecurityDepositInfo securityDepositInfo = orders.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo();
                    orderPriceInfoResponse.setSecurityDepositInfo(OrderPriceInfoResponse.SecurityDepositInfo.builder()
                        .amount(securityDepositInfo.getSecurityDeposit())
                        .paidDate(Optional.ofNullable(securityDepositInfo.getSecurityDepositDate()).map(Date::toInstant).orElse(null))
                        .refundDate(Optional.ofNullable(securityDepositInfo.getRefundSecurityDepositDate()).map(Date::toInstant).orElse(null))
                        .manualRefundDate(Optional.ofNullable(securityDepositInfo.getManualRefundUpdateDate()).map(Date::toInstant).orElse(null))
                        .build());
                }
                orderPriceInfoResponse.setRelatedPriceInfos(getRefOrderPriceInfoListByPayId(orderPriceInfoResponse.getId())
                    .stream().map(RelatedPriceInfo::new).collect(Collectors.toList()));
            })
            .sorted(comparator)
            .collect(Collectors.groupingBy(OrderPriceInfoResponse::getStage,
                Collectors.collectingAndThen(Collectors.toList(),
                    list -> new StageOrderPriceInfoResponse(list))));
    }

    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateCsv(OrderPriceInfoCriteriaRequest request) {
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        BeanUtils.copyProperties(request, criteria);
        List<OrderPriceInfoWithDescription> orderPriceInfoList = getNonZeroAmountPriceInfos(criteria, null, null);

        Map<String, Orders> ordersMap = orderService.getOrders(new ArrayList<>(orderPriceInfoList.stream().map(OrderPriceInfo::getOrderNo).collect(Collectors.toSet())))
            .stream().collect(Collectors.toMap(Orders::getOrderNo, Function.identity()));
        Map<String, Integer> orderAcctIdMap = ordersMap.values().stream().collect(Collectors.toMap(Orders::getOrderNo, order -> order.getContract().getMainContract().getAcctId()));
        Map<Integer, AuthUser> userMap = authServer.getUserAcctIds(orderAcctIdMap.values().toArray(new Integer[0])).stream().collect(Collectors.toMap(AuthUser::getAcctId, user -> user));
        List<PriceInfoCSV> csvList = orderPriceInfoList.stream().map(opi -> {
            AuthUser authUser = userMap.get(orderAcctIdMap.get(opi.getOrderNo()));
            String plateNo = ordersMap.get(opi.getOrderNo()).getPlateNo();
            return new PriceInfoCSV(opi, plateNo, authUser);
        }).sorted(Comparator.comparing(PriceInfoCSV::getId)).collect(Collectors.toList());
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            csvList,
            PriceInfoCSV.HEADS,
            true,
            ',',
            out,
            Charset.forName("big5"),
            PriceInfoCSV.class
        );
        return out;
    }

    /**
     * 處理車損費用
     */
    @Deprecated
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void calculateAccident(Orders order, int amount) {
        List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrderAndAcctId(order.getOrderNo(), order.getContract().getMainContract().getAcctId())
            .stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == CarAccident).collect(Collectors.toList());
        List<OrderPriceInfo> saveList = new ArrayList<>();
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(order);
        CalculateStage calculateStage = calculateStageList.get(calculateStageList.size() - 1);
        int paid = 0;
        int unPaid = 0;
        int refund = 0;
        int unRefund = 0;
        int total = 0;
        List<OrderPriceInfo> flexUnPaidPriceInfos = new ArrayList<>();
        List<OrderPriceInfo> flexUnRefundPriceInfos = new ArrayList<>();
        // 拿取車損各種款項資訊
        if (orderPriceInfoList.size() > 0) {
            for (OrderPriceInfo orderPriceInfo : orderPriceInfoList) {
                switch (orderPriceInfo.getType()) {
                    case 0:
                        // 計算應收未收
                        if (orderPriceInfo.getAmount() > 0 && orderPriceInfo.getReceivedAmount() > 0) {
                            paid += orderPriceInfo.getReceivedAmount();
                        } else {
                            unPaid += orderPriceInfo.getAmount();
                            // 拿取未收款的OrderPriceInfo，以處理後面應收異動的金額
                            flexUnPaidPriceInfos.add(orderPriceInfo);
                        }
                        break;
                    case 1:
                        // 計算以使用折扣未使用折扣，基本不會進入該case
                        if (orderPriceInfo.getAmount() > 0 && orderPriceInfo.getReceivedAmount() > 0) {
                            paid -= orderPriceInfo.getReceivedAmount();
                        } else {
                            unPaid -= orderPriceInfo.getAmount();
                            // 拿取未折扣的OrderPriceInfo，以處理後面應收異動的金額
                            flexUnPaidPriceInfos.add(orderPriceInfo);
                        }
                        break;
                    case 2:
                        // 計算已退未退
                        if (orderPriceInfo.getAmount() > 0 && orderPriceInfo.getReceivedAmount() > 0) {
                            refund += orderPriceInfo.getReceivedAmount();
                        } else {
                            unRefund += orderPriceInfo.getAmount();
                            // 拿取未退款的OrderPriceInfo，以處理後面應收異動的金額
                            flexUnRefundPriceInfos.add(orderPriceInfo);
                        }
                        break;
                    default:
                }
            }
            total = paid + unPaid - refund - unRefund;
            // 新應收大於舊應收
            if (amount - total > 0) {
                int gainPaid = amount - total;
                // 若有未收款，則增加未收款金額
                if (!flexUnRefundPriceInfos.isEmpty()) {
                    // 查詢是否有尚未退款金額，若有則減少退款金額
                    for (OrderPriceInfo flexUnRefundPriceInfo : flexUnRefundPriceInfos) {
                        if (flexUnRefundPriceInfo.getAmount() >= gainPaid) {
                            flexUnRefundPriceInfo.setAmount(flexUnRefundPriceInfo.getAmount() - gainPaid);
                            saveList.add(flexUnRefundPriceInfo);
                            gainPaid = 0;
                            break;
                        } else {
                            gainPaid -= flexUnRefundPriceInfo.getAmount();
                            flexUnRefundPriceInfo.setAmount(0);
                            saveList.add(flexUnRefundPriceInfo);

                        }
                    }


                } else if (!flexUnPaidPriceInfos.isEmpty()) {
                    flexUnPaidPriceInfos.get(0).setAmount(flexUnPaidPriceInfos.get(0).getAmount() + (gainPaid));
                    gainPaid = 0;
                    saveList.add(flexUnPaidPriceInfos.get(0));
                }
                if (gainPaid > 0) {
                    OrderPriceInfo flexUnPaidPriceInfo = new OrderPriceInfo();
                    flexUnPaidPriceInfo.setStage(calculateStage.getStage());
                    flexUnPaidPriceInfo.setType(Pay.getCode());
                    flexUnPaidPriceInfo.setLastPayDate(calculateStage.getEndDate());
                    flexUnPaidPriceInfo.setAmount(gainPaid);
                    flexUnPaidPriceInfo.setCategory(CarAccident);
                    flexUnPaidPriceInfo.setOrderNo(order.getOrderNo());
                    saveList.add(flexUnPaidPriceInfo);
                }
            } else if (amount - total < 0) {
                int refundAmt = Math.abs(amount - total);
                // 若有應收款，先減未收的應收款
                if (!flexUnPaidPriceInfos.isEmpty()) {
                    for (OrderPriceInfo flexUnPaidPriceInfo : flexUnPaidPriceInfos) {
                        if (flexUnPaidPriceInfo.getAmount() >= refundAmt) {
                            flexUnPaidPriceInfo.setAmount(flexUnPaidPriceInfo.getAmount() - refundAmt);
                            saveList.add(flexUnPaidPriceInfo);
                            refundAmt = 0;
                            break;
                        } else {
                            refundAmt -= flexUnPaidPriceInfo.getAmount();
                            flexUnPaidPriceInfo.setAmount(0);
                            saveList.add(flexUnPaidPriceInfo);

                        }
                    }
                }
                // 新應收小於舊應收，需補退款
                if (!flexUnRefundPriceInfos.isEmpty() && refundAmt > 0) {

                    Set<Integer> refPayIds = new HashSet<>();
                    for (OrderPriceInfo flexUnRefundPriceInfo : flexUnRefundPriceInfos) {
                        if (refPayIds.contains(flexUnRefundPriceInfo.getRefPriceInfoNo())) {
                            continue;
                        }
                        refPayIds.add(flexUnRefundPriceInfo.getRefPriceInfoNo());
                        // 拿取該筆款項剩餘可退金額，增加退款金額
                        int refundableAmt = calculateRefundAvailableAmt(flexUnRefundPriceInfo.getRefPriceInfoNo());
                        if (refundableAmt > 0) {
                            flexUnRefundPriceInfo.setAmount(flexUnRefundPriceInfo.getAmount()
                                + (refundableAmt >= refundAmt ? refundAmt : (refundAmt - refundableAmt)));
                            refundAmt -= refundableAmt;
                            saveList.add(flexUnRefundPriceInfo);
                        }
                        if (refundAmt <= 0) {
                            break;
                        }
                    }
                    // 若尚未退款的金額尚消化完，則找尋可退款的費用產生新退款金額
                    if (refundAmt >= 0) {
                        saveList.addAll(refundAndSplitAmount(order.getOrderNo(), new AtomicReference<>(Math.abs(refundAmt - total)), CarAccident, CarAccident).stream().peek(p -> p.setReceivableDate(Instant.now())).collect(Collectors.toList()));
                    }
                } else if (flexUnPaidPriceInfos.isEmpty() && refundAmt > 0) {
                    // 產生退款
                    saveList.addAll(refundAndSplitAmount(order.getOrderNo(), new AtomicReference<>(Math.abs(amount - total)), CarAccident, CarAccident).stream().peek(p -> p.setReceivableDate(Instant.now())).collect(Collectors.toList()));
                }
            }
        } else {
            if (amount > 0) {
                OrderPriceInfo flexUnPaidPriceInfo = new OrderPriceInfo();
                flexUnPaidPriceInfo.setStage(calculateStage.getStage());
                flexUnPaidPriceInfo.setType(Pay.getCode());
                flexUnPaidPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
                flexUnPaidPriceInfo.setLastPayDate(DateUtil.convertToStartOfInstant(Instant.now().atZone(DateUtils.ZONE_TPE).plus(1, MONTHS).minus(1, MILLIS).toInstant()));
                flexUnPaidPriceInfo.setAmount(amount);
                flexUnPaidPriceInfo.setCategory(CarAccident);
                flexUnPaidPriceInfo.setOrderNo(order.getOrderNo());
                saveList.add(flexUnPaidPriceInfo);

            } else {
                throw new SubscribeException(REFUND_AMOUNT_OVER_PAY_AMOUNT);
            }
        }
        if (!saveList.isEmpty()) {
            orderPriceInfoRepository.saveAll(saveList);
        }
    }

    /**
     * 取得使用者已付款金額
     */
    public Integer paidAmt(String orderNo) {
        return paidAmt(Collections.singletonList(orderNo), true, false).get(orderNo);
    }

    /**
     * 取得使用者已付款金額
     */
    public Map<String, Integer> paidAmt(List<String> orderNos, boolean filterEtag, boolean filterSecurityDeposit) {
        List<OrderPriceInfo> orderPriceInfos = getPriceInfosByOrders(orderNos);
        return orderPriceInfos.stream().filter(orderPriceInfo -> Optional.ofNullable(orderPriceInfo.getReceivedAmount())
                .orElse(0) > 0
                && (!filterEtag || !orderPriceInfo.getCategory().equals(ETag))
                && (!filterSecurityDeposit || !orderPriceInfo.getCategory().equals(SecurityDeposit)))
            .collect(Collectors.groupingBy(OrderPriceInfo::getOrderNo, Collectors.summingInt(orderPriceInfo -> (orderPriceInfo.getType() == Pay.getCode() ? 1 : -1) * orderPriceInfo.getReceivedAmount())));
    }

    /**
     * 取得使用者已付款金額
     */
    public Integer paidRentAmt(String orderNo) {
        List<OrderPriceInfo> orderPriceInfos = getPriceInfosByOrder(orderNo);
        return orderPriceInfos.stream().filter(orderPriceInfo -> Optional.ofNullable(orderPriceInfo.getReceivedAmount())
                .orElse(0) > 0 && !orderPriceInfo.getCategory().equals(ETag)
                && !orderPriceInfo.getCategory().equals(SecurityDeposit))
            .mapToInt(PriceInfoInterface::getActualReceivePrice).sum();
    }

    /**
     * 取得使用者欲退款金額
     */
    public Integer refundAmt(String orderNo) {
        List<OrderPriceInfo> orderPriceInfos = getPriceInfosByOrder(orderNo);
        return orderPriceInfos.stream().filter(orderPriceInfo -> !orderPriceInfo.getCategory().equals(ETag)
                && orderPriceInfo.getType() == Refund.getCode())
            .filter(orderPriceInfo -> orderPriceInfo.getInfoDetail() == null || Objects.equals(orderPriceInfo.getInfoDetail().getIsAgree(), Boolean.TRUE))
            .mapToInt(opi -> opi.getAmount() - opi.getReceivedAmount()).sum();
    }

    /**
     * 取得使用者欲折扣金額
     */
    public Integer discountAmt(String orderNo) {
        List<OrderPriceInfo> orderPriceInfos = getPriceInfosByOrder(orderNo);
        return orderPriceInfos.stream().filter(orderPriceInfo -> !orderPriceInfo.getCategory().equals(ETag)
                && orderPriceInfo.getType() == Discount.getCode())
            .filter(opi -> opi.getReceivableDate().isBefore(Instant.now()))
            .filter(orderPriceInfo -> orderPriceInfo.getInfoDetail() == null || Objects.equals(orderPriceInfo.getInfoDetail().getIsAgree(), Boolean.TRUE) || orderPriceInfo.getCategory().equals(MileageFee))
            .mapToInt(OrderPriceInfo::getReceivedAmount).sum();
    }

    public List<OrderPriceInfo> getUserUnPaidPriceInfoByOrder(int acctId, String orderNo) {
        return getUserUnPaidPriceInfoByOrder(acctId, orderNo, false);
    }

    /**
     * 拿取使用者須付費但未付費資訊
     */
    public List<OrderPriceInfo> getUserUnPaidPriceInfoByOrder(int acctId, String orderNo, boolean history) {

        List<OrderPriceInfo> priceInfos = getPriceInfosByOrderAndAcctId(orderNo, acctId);
        priceInfos = priceInfos.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivableDate().compareTo(Instant.now()) <= 0
            && orderPriceInfo.getType() != Refund.getCode()
            && (history || !orderPriceInfo.isPaid())).collect(Collectors.toList());
        return priceInfos;
    }

    /**
     * 拿取訂單須付費但未付費資訊
     */
    public List<OrderPriceInfo> getUnPaidPriceInfoByOrder(String orderNo, boolean history) {

        List<OrderPriceInfo> priceInfos = getPriceInfosByOrder(orderNo);
        priceInfos = priceInfos.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivableDate().compareTo(Instant.now()) <= 0
                && !(orderPriceInfo.getType() == Refund.getCode() && orderPriceInfo.getCategory() == SecurityDeposit)
                && (history || !orderPriceInfo.isPaid()))
            .sorted(Comparator.comparing(OrderPriceInfo::getCategory, Comparator.comparing(PriceInfoDefinition.PriceInfoCategory::getSortIndex))).collect(Collectors.toList());
        return priceInfos;
    }

    /**
     * 拿取使用者須付費但未付費資訊(前端用)
     */
    public List<OrderPriceInfoResponse> getUserUnPaidPriceInfoByOrderResponse(int acctId, String orderNo, boolean history, boolean isCredit, boolean isLastMileage) {
        Orders orders = orderService.getOrder(orderNo);
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(orders);
        // 預先取得所有 Sku 資料
        Map<String, Sku> allSkus = skuService.getAllSkus();
        return getUserUnPaidPriceInfoByOrder(acctId, orderNo, history).stream().map(opi -> new OrderPriceInfoResponse(opi, allSkus))
            .filter(opi -> (isCredit || StringUtils.isBlank(opi.getUid())
                || (StringUtils.isNotBlank(opi.getUid()) && Optional.ofNullable(opi.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(false)))
                // 是否包含最後一筆里程費
                && !(!isLastMileage && opi.getCategory().equals(MileageFee) && opi.getStage().equals(calculateStageList.size()) && orders.getReturnMileage() == null))
            .peek(orderPriceInfoResponse -> orderPriceInfoResponse.setCalculateStage(calculateStageList.get(orderPriceInfoResponse.getStage() - 1)))
            .sorted(Comparator.comparing(OrderPriceInfoResponse::getCategory, Comparator.comparing(PriceInfoDefinition.PriceInfoCategory::getSortIndex))).collect(Collectors.toList());
    }

    /**
     * 拿取訂單須付費但未付費資訊(前端用)
     */
    public List<OrderPriceInfoResponse> getUnPaidPriceInfoByOrderResponse(String orderNo, boolean history, boolean isCredit) {
        Orders orders = orderService.getOrder(orderNo);
        List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(orders);
        // 預先取得所有 Sku 資料
        Map<String, Sku> allSkus = skuService.getAllSkus();
        return getUnPaidPriceInfoByOrder(orderNo, history).stream().map(opi -> new OrderPriceInfoResponse(opi, allSkus))
            .filter(opi -> isCredit || StringUtils.isBlank(opi.getUid())
                || (StringUtils.isNotBlank(opi.getUid()) && Optional.ofNullable(opi.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(false)))
            .peek(orderPriceInfoResponse -> orderPriceInfoResponse.setCalculateStage(calculateStageList.get(orderPriceInfoResponse.getStage() - 1))).collect(Collectors.toList());
    }

    /**
     * 拿取使用者主約須付費但未付費資訊
     */
    public List<OrderPriceInfo> getUserUnPaidPriceInfo(int acctId, String mainContractNo) {

        List<OrderPriceInfo> priceInfos = new ArrayList<>();

        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setAcctId(Collections.singletonList(acctId));
        criteria.setMainContractNo(mainContractNo);

        List<String> orderNos = orderService.searchByPage(new PageRequest(Integer.MAX_VALUE, 0), criteria).getList().stream().map(OrderQueryResponse::getOrderNo).collect(Collectors.toList());
        for (String orderNo : orderNos) {
            priceInfos.addAll(getPriceInfosByOrderAndAcctId(orderNo, acctId));
        }
        priceInfos = priceInfos.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivableDate().compareTo(Instant.now()) <= 0
            && orderPriceInfo.getType() != Refund.getCode()).collect(Collectors.toList());
        return priceInfos;
    }

    public CommonOrderPriceInfoResponse generatePriceInfoResponse(Orders orders) {
        MainContract mainContract = orders.getContract().getMainContract();
        CommonOrderPriceInfoResponse priceInfoResponse = new CommonOrderPriceInfoResponse();
        if (orders.getStatus() <= OrderStatus.CREDITED.getStatus() && orders.getIsNewOrder()) {
            priceInfoResponse = new CommonOrderPriceInfoResponse(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit(), orders.getExpectStartDate());
        } else {
            List<OrderPriceInfo> orderPriceInfoList = getUserUnPaidPriceInfoByOrder(mainContract.getAcctId(), orders.getOrderNo());
            if (!orderPriceInfoList.isEmpty()) {
                int amt = orderPriceInfoList.stream().mapToInt(PriceInfoInterface::getActualPrice).sum();
                Instant minDate = orderPriceInfoList.stream().min(Comparator.comparing(OrderPriceInfo::getLastPayDate)).map(OrderPriceInfo::getLastPayDate).orElse(Instant.now());
                priceInfoResponse = new CommonOrderPriceInfoResponse(amt, minDate);
            }
        }
        return priceInfoResponse;
    }

    /**
     * 取得應收金額
     */
    public int getReceivableAmount(String orderNo) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(orderNo)).build())
            .stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() != SecurityDeposit).mapToInt(PriceInfoInterface::getActualPrice).sum();
    }

    /**
     * 取得到當下應收金額，不包含保證金，且日期為最後付款日前10天與最後付款日小於等於出車時間
     */
    public int getCurrentReceivableAmount(String orderNo) {
        return getCurrentReceivablePriceInfo(orderNo).stream().mapToInt(PriceInfoInterface::getActualPrice).sum();
    }

    /**
     * 取得到當下應收金額，不包含保證金，且日期為最後付款日前10天與最後付款日小於等於出車時間
     */
    public List<OrderPriceInfo> getCurrentReceivablePriceInfo(String orderNo) {
        List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrder(orderNo);
        return orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() != SecurityDeposit
            && orderPriceInfo.getCategory() != ETag
            && (orderPriceInfo.getReceivableDate().compareTo(DateUtil.convertToEndOfInstant(Instant.now())) <= 0
            || orderPriceInfo.getReceivedAmount() != 0)
            && (orderPriceInfo.getRefPriceInfoNo() == null || Optional.ofNullable(orderPriceInfo.getRefPriceInfo()).map(OrderPriceInfo::getCategory).orElse(null) != SecurityDeposit)
        ).collect(Collectors.toList());
    }

    /**
     * 取得到當下已收金額，不包含保證金，且日期為最後付款日前10天與最後付款日小於等於出車時間
     */
    public List<OrderPriceInfo> getCurrentReceivedPriceInfo(String orderNo) {
        List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrder(orderNo);
        return orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() != SecurityDeposit
            && orderPriceInfo.getCategory() != ETag
            && (orderPriceInfo.getReceivableDate().compareTo(DateUtil.convertToEndOfInstant(Instant.now())) <= 0
            && orderPriceInfo.getReceivedAmount() != 0)
        ).collect(Collectors.toList());
    }

    /**
     * 退訂費用清單明細
     */
    public List<OrderPriceInfoResponse> getCancelPriceInfo(String orderNo, int acctId) {
        Orders orders = orderService.getUserOrder(orderNo, acctId);
        return getCancelPriceInfo(orders.getOrderNo());
    }

    /**
     * 退訂費用清單明細
     */
    public List<OrderPriceInfoResponse> getCancelPriceInfo(String orderNo) {
        List<OrderPriceInfo> cancelBookingList = getPriceInfosByOrder(orderNo, CancelBooking, null);
        if (cancelBookingList.isEmpty()) {
            return new ArrayList<>();
        }
        List<OrderPriceInfoResponse> responseList = new ArrayList<>();
        Map<Integer, OrderPriceInfoResponse> refMapping = new HashMap<>();
        OrderPriceInfoResponse securityDeposit = null;
        for (OrderPriceInfo cancelBooking : cancelBookingList) {
            OrderPriceInfo orderPriceInfo = get(cancelBooking.getRefPriceInfoNo());
            // 保證金退款額外處理
            if (orderPriceInfo.getCategory() == SecurityDeposit) {
                securityDeposit = new OrderPriceInfoResponse(orderPriceInfo);
                securityDeposit.setCategoryName("退訂手續費");
                securityDeposit.setAmount(orderPriceInfo.getAmount() - cancelBooking.getAmount());
                securityDeposit.setFormulaDescription(String.format("($%dx%.2f)", orderPriceInfo.getAmount(), (orderPriceInfo.getAmount() - cancelBooking.getAmount()) * 1.0 / orderPriceInfo.getAmount()));
            } else {
                OrderPriceInfoResponse orderPriceInfoResponse = new OrderPriceInfoResponse(orderPriceInfo);
                responseList.add(new OrderPriceInfoResponse(orderPriceInfo));
                OrderPriceInfoResponse cancelBookingResp = new OrderPriceInfoResponse(cancelBooking);
                cancelBookingResp.setCategoryName(
                    Optional.ofNullable(of(cancelBookingResp.getType())).map(PriceInfoDefinition.PriceInfoType::getDescriptionName).orElse("") + orderPriceInfoResponse.getCategoryName());
                refMapping.put(orderPriceInfo.getId(), cancelBookingResp);
            }
        }
        responseList = responseList.stream().sorted(Comparator.comparing(OrderPriceInfoResponse::getCategory, Comparator.comparing(PriceInfoDefinition.PriceInfoCategory::getSortIndex))).collect(Collectors.toList());
        for (Integer id : responseList.stream().map(OrderPriceInfoResponse::getId).collect(Collectors.toList())) {
            responseList.add(refMapping.get(id));
        }
        if (securityDeposit != null) {
            responseList.add(securityDeposit);
        }

        return responseList;
    }

    /**
     * 取得已收金額
     */
    public int getPaidAmount(String orderNo) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(orderNo)).build())
            .stream().filter(orderPriceInfo -> (orderPriceInfo.getType() != Discount.getCode())).mapToInt(orderPriceInfo ->
                (orderPriceInfo.getType() == Pay.getCode() ? 1 : -1) * orderPriceInfo.getReceivedAmount()
            ).sum();
    }

    /**
     * 取得未收
     */
    public int getUnPaidAmount(String orderNo, boolean ignoreSecurityDeposit) {
        return getPriceInfosByOrder(orderNo).stream()
            .filter(orderPriceInfo -> !orderPriceInfo.isPaid() && (!ignoreSecurityDeposit || !orderPriceInfo.getCategory().equals(SecurityDeposit)))
            .mapToInt(PriceInfoInterface::getActualPrice).sum();
    }

    /**
     * 取得未收
     */
    public int getTotalAmount(String orderNo, boolean filterSecurityDeposit) {
        return getPriceInfosByOrder(orderNo)
            .stream().filter(orderPriceInfo -> (!filterSecurityDeposit || !orderPriceInfo.getCategory().equals(SecurityDeposit))).mapToInt(priceInfo -> priceInfo.getActualPrice()).sum();
    }

    /**
     * 取得未收
     */
    public Map<String, Integer> getTotalAmount(List<String> orderNos, boolean filterSecurityDeposit) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(orderNos).build())
            .stream().filter(orderPriceInfo -> (!filterSecurityDeposit || !orderPriceInfo.getCategory().equals(SecurityDeposit)))
            .collect(Collectors.groupingBy(OrderPriceInfo::getOrderNo, Collectors.summingInt(priceInfo -> priceInfo.getActualPrice())));
    }

    /**
     * 檢查訂單是否有未繳款項
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public Orders checkIsUnpaid(Orders order) {
        if (order.getStatus() < OrderStatus.CREDITED.getStatus()) {
            order.setIsUnpaid(false);
        } else {
            List<OrderPriceInfo> unpaidList = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.orderNo), order.getOrderNo()));
                predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.type), Pay.getCode()));
                predicates.add(builder.notEqual(root.get(OrderPriceInfo.Fields.amount), 0));
                predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.receivedAmount), 0));

                predicates.add(builder.lessThanOrEqualTo(root.get(OrderPriceInfo.Fields.receivableDate), new Date().toInstant()));
                return builder.and(predicates.toArray(new Predicate[0]));
            });
            int amt = unpaidList.stream().mapToInt(PriceInfoInterface::getActualPrice).sum();
            if (amt <= 0) {
                log.info("訂單 {} 無未繳款項", order.getOrderNo());
                order.setIsUnpaid(false);
            } else {
                log.info("訂單 {} 有未繳款項", order.getOrderNo());
                order.setIsUnpaid(true);
            }
        }
        return order;
    }

    /**
     * 異動保證金可付款期限
     */
    public void updateSecurityDepositLastPayDate(String orderNo) {
        List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrder(orderNo, SecurityDeposit, Pay).stream()
            .filter(orderPriceInfo -> !orderPriceInfo.isPaid()).peek(orderPriceInfo -> {
                orderPriceInfo.setReceivableDate(Instant.now());
                orderPriceInfo.setLastPayDate(Instant.now().plus(1, DAYS));
            }).collect(Collectors.toList());
        orderPriceInfoRepository.saveAll(orderPriceInfoList);
    }

    /**
     * 取得門市折扣
     */
    public int getEmpDiscountAmount(String orderNo) {
        return getPriceInfosByOrder(orderNo)
            .stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == EmpDiscount).mapToInt(orderPriceInfo ->
                (orderPriceInfo.getType() == Pay.getCode() ? 1 : -1) * orderPriceInfo.getReceivedAmount()
            ).sum();
    }

    /**
     * 還車時，清除未處理的額外費用與提前換車退款
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void cleanReturnCarUnDoInfo(String orderNo) {
        orderPriceInfoRepository.deleteNotPaidEmpDiscardFee(orderNo);
        orderPriceInfoRepository.deleteNotPaidReturnFee(orderNo);
    }

    /**
     * 取得各類付款金額
     */
    public Map<PriceInfoDefinition.AmtCategory, Integer> getAllCategoryAmt(String orderNo) {
        Map<PriceInfoDefinition.AmtCategory, Integer> result = new HashMap<>();
        int paidAmt = 0;
        int unPaidAmt = 0;
        int empDisAmt = 0;
        int receivableAmt = 0;
        int originalReceivableAmt = 0;
        int rentAmt = 0;
        int extAmt = 0;
        int accidentAmt = 0;
        int accidentReceivableAmt = 0;
        int cancelAmt = 0;
        List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrder(orderNo);

        for (OrderPriceInfo orderPriceInfo : orderPriceInfoList) {
            int isPositive = orderPriceInfo.getType() == Pay.getCode() ? 1 : -1;
            switch (orderPriceInfo.getCategory()) {
                case MonthlyFee:
                case MileageFee:
                    paidAmt += orderPriceInfo.getReceivedAmount() * isPositive;
                    if (orderPriceInfo.getReceivedAmount() == 0) {
                        unPaidAmt += orderPriceInfo.getAmount() * isPositive;
                    }
                    receivableAmt += orderPriceInfo.getAmount() * isPositive;
                    rentAmt += orderPriceInfo.getAmount() * isPositive;
                    if (isPositive == 1) {
                        originalReceivableAmt += orderPriceInfo.getAmount();
                    }
                    break;
                case EmpDiscount:
                    empDisAmt += orderPriceInfo.getAmount();
                    receivableAmt += orderPriceInfo.getAmount() * isPositive;
                    break;
                case Dispatch:
                case Insurance:
                    extAmt += orderPriceInfo.getAmount() * isPositive;
                    receivableAmt += orderPriceInfo.getAmount() * isPositive;
                    break;
                case ETag:
                case CarAccident:
                    paidAmt += orderPriceInfo.getReceivedAmount() * isPositive;
                    accidentReceivableAmt += Optional.ofNullable(orderPriceInfo.getInfoDetail()).map(PriceInfoDetail::getOriginAmount).orElse(orderPriceInfo.getAmount()) * isPositive;
                    accidentAmt += orderPriceInfo.getReceivedAmount();
                    if (orderPriceInfo.getReceivedAmount() == 0) {
                        unPaidAmt += orderPriceInfo.getAmount() * isPositive;
                    }
                    receivableAmt += orderPriceInfo.getAmount() * isPositive;
                    break;
                case CancelBooking:
                    OrderPriceInfo ref = orderPriceInfoList.stream().filter(o -> Objects.equals(orderPriceInfo.getRefPriceInfoNo(), o.getRefPriceInfoNo())).findAny().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
                    cancelAmt += orderPriceInfo.getAmount() * isPositive;
                    paidAmt += orderPriceInfo.getReceivedAmount() * isPositive;
                    if (orderPriceInfo.getReceivedAmount() == 0) {
                        unPaidAmt += orderPriceInfo.getAmount() * isPositive;
                    }
                    paidAmt += ref.getReceivedAmount() * isPositive;
                    if (ref.getReceivedAmount() == 0) {
                        unPaidAmt += ref.getAmount() * isPositive;
                    }
                    break;
                default:
            }
        }
        result.put(PriceInfoDefinition.AmtCategory.ACCIDENT_AMOUNT, accidentAmt);
        result.put(PriceInfoDefinition.AmtCategory.ACCIDENT_RECEIVABLE_AMOUNT, accidentReceivableAmt);
        result.put(PriceInfoDefinition.AmtCategory.EXT_AMOUNT, extAmt);
        result.put(PriceInfoDefinition.AmtCategory.PAID_AMOUNT, paidAmt);
        result.put(PriceInfoDefinition.AmtCategory.RECEIVABLE_AMOUNT, receivableAmt);
        result.put(PriceInfoDefinition.AmtCategory.EMP_DISCOUNT_AMOUNT, empDisAmt);
        result.put(PriceInfoDefinition.AmtCategory.RENT_AMOUNT, rentAmt);
        result.put(PriceInfoDefinition.AmtCategory.UNPAID_AMOUNT, unPaidAmt);
        result.put(PriceInfoDefinition.AmtCategory.ORIGINAL_RECEIVABLE_AMOUNT, originalReceivableAmt);
        result.put(PriceInfoDefinition.AmtCategory.CANCEL_AMOUNT, cancelAmt);
        return result;
    }

    /**
     * 檢查是否還可退款
     */
    public boolean orderPriceInfoCanRefund(Integer orderPriceInfoId, Integer amount) {
        return calculateRefundAvailableAmt(orderPriceInfoId) >= amount;
    }

    /**
     * 計算可退款額度
     */
    public int calculateRefundAvailableAmt(Integer orderPriceInfoId) {
        OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(orderPriceInfoId)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        AtomicInteger receivedAmount = new AtomicInteger(orderPriceInfo.getReceivedAmount());
        OrderPriceInfoCriteria criteria = OrderPriceInfoCriteria.builder().refPriceInfoNo(orderPriceInfo.getId()).build();
        List<OrderPriceInfo> refOrderPriceInfos = orderPriceInfoRepository.getPriceInfos(criteria);
        refOrderPriceInfos.stream().filter(OrderPriceInfo::isValidRefOrderPriceInfo)
            .forEach(ref -> receivedAmount.addAndGet(-ref.getAmount()));
        return receivedAmount.get();
    }

    /**
     * 計算可折價額度
     */
    public int calculateDiscountAvailableAmt(Integer orderPriceInfoId) {
        OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(orderPriceInfoId)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        if (orderPriceInfo.getReceivedAmount() > 0 || orderPriceInfo.isPaid()) {
            throw new SubscribeException(ORDER_PRICE_INFO_HAVE_PAID);
        }
        AtomicInteger receivedAmount = new AtomicInteger(orderPriceInfo.getAmount());
        OrderPriceInfoCriteria criteria = OrderPriceInfoCriteria.builder().refPriceInfoNo(orderPriceInfo.getId()).build();
        List<OrderPriceInfo> refOrderPriceInfos = orderPriceInfoRepository.getPriceInfos(criteria);
        refOrderPriceInfos.stream().filter(OrderPriceInfo::isValidRefOrderPriceInfo)
            .forEach(ref -> receivedAmount.addAndGet(-ref.getAmount()));
        return receivedAmount.get();
    }

    /**
     * 透過ID取得退款編號
     */
    public List<OrderPriceInfo> getRefundOrderPriceInfoListByIds(List<Integer> refundIds) {
        return orderPriceInfoRepository.findAllById(refundIds).stream().filter(orderPriceInfo -> orderPriceInfo.getType() == Refund.getCode()).collect(Collectors.toList());
    }

    /**
     * 透過付款編號拿取相依的清單
     */
    public List<OrderPriceInfo> getRefOrderPriceInfoListByPayId(Integer payId) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().refPriceInfoNo(payId).build());
    }

    /**
     * 產生全額退款款項，若尚未付款則金額改為0，不發動退款
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<OrderPriceInfo> refundAll(Integer orderPriceInfoId, @Nullable PriceInfoDefinition.PriceInfoCategory category) {

        OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(orderPriceInfoId).orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        if (orderPriceInfo.getReceivedAmount() == 0 && orderPriceInfo.getAmount() > 0) {
            orderPriceInfo.setAmount(0);
            List<OrderPriceInfo> refList = getRefOrderPriceInfoListByPayId(orderPriceInfo.getId());
            refList.forEach(o -> o.setAmount(0));
            orderPriceInfoRepository.saveAll(refList);
            orderPriceInfoRepository.save(orderPriceInfo);
        } else {
            int paid = orderPriceInfo.getReceivedAmount();
            List<OrderPriceInfo> refList = getRefOrderPriceInfoListByPayId(orderPriceInfo.getId());
            OrderPriceInfo refund = null;
            for (OrderPriceInfo ref : refList) {
                if (refund == null && ref.getPaymentId() == null && ref.getType() == Refund.getCode() && ref.getAmount() > 0 && ref.getReceivedAmount() == 0) {
                    refund = ref;
                }
                paid -= ref.getAmount();
            }
            // 將尚未退款/折扣的金額加上差額
            if (paid > 0) {
                if (refund == null) {
                    storedRefundRecord(orderPriceInfoId, paid, category);
                } else {
                    refund.setAmount(refund.getAmount() + paid);
                    orderPriceInfoRepository.saveAndFlush(refund);
                }
            }
        }
        return getRefOrderPriceInfoListByPayId(orderPriceInfo.getId());
    }

    /**
     * 針對指定Category進行退款，並自動拆分金額
     */
    public List<OrderPriceInfo> refundAndSplitAmount(String orderNo, AtomicReference<Integer> amount, PriceInfoDefinition.PriceInfoCategory category, PriceInfoDefinition.PriceInfoCategory negativeCategory) {
        List<OrderPriceInfo> refundAbleList = new ArrayList<>();
        List<OrderPriceInfo> orderPriceInfoList = getPriceInfosByOrder(orderNo, category, Pay).stream()
            .filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() > 0
                && (orderPriceInfo.getPaymentId() != null || orderPriceInfo.getRemitAccountIds() != null && !orderPriceInfo.getRemitAccountIds().isEmpty())
                && !orderPriceInfo.getCategory().equals(ETag)
                && !orderPriceInfo.getCategory().equals(SecurityDeposit)).collect(Collectors.toList());

        // 先網刷退款
        for (OrderPriceInfo orderPriceInfo : orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getPaymentId() != null).collect(Collectors.toList())) {
            OrderPriceInfo refundPriceInfo = generateNegativeOrderPriceInfo(orderPriceInfo, amount.get(), negativeCategory);
            if (refundPriceInfo != null) {
                amount.set(amount.get() - refundPriceInfo.getAmount());
                refundAbleList.add(refundPriceInfo);
            }
            if (amount.get() <= 0) {
                break;
            }
        }

        if (amount.get() > 0) {
            // 匯款退款
            for (OrderPriceInfo orderPriceInfo : orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getRemitAccountIds() != null && !orderPriceInfo.getRemitAccountIds().isEmpty()).collect(Collectors.toList())) {
                OrderPriceInfo refundPriceInfo = generateNegativeOrderPriceInfo(orderPriceInfo, amount.get(), negativeCategory);
                if (refundPriceInfo != null) {
                    amount.set(amount.get() - refundPriceInfo.getAmount());
                    refundAbleList.add(refundPriceInfo);
                }
                if (amount.get() <= 0) {
                    break;
                }
            }
        }
        if (amount.get() > 0) {
            throw new SubscribeException(REFUND_AMOUNT_OVER_PAY_AMOUNT);
        }
        return refundAbleList;
    }

    /**
     * 針對未付款進行折扣，並自動拆分金額
     */
    public List<OrderPriceInfo> discountAndSplitAmount(String orderNo, AtomicReference<Integer> amount, PriceInfoDefinition.PriceInfoCategory category) {
        List<OrderPriceInfo> discountableList = new ArrayList<>();
        List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(orderNo)).type(Pay.getCode()).build()).stream()
            .filter(orderPriceInfo ->
                orderPriceInfo.getReceivedAmount() == 0
                    && !orderPriceInfo.isPaid() && orderPriceInfo.getAmount() > 0
                    && !orderPriceInfo.getCategory().equals(ETag)
                    && !orderPriceInfo.getCategory().equals(SecurityDeposit))
            .collect(Collectors.toList());
        for (OrderPriceInfo orderPriceInfo : orderPriceInfoList) {
            OrderPriceInfo discountPriceInfo = generateNegativeOrderPriceInfo(orderPriceInfo, amount.get(), category);
            if (discountPriceInfo != null) {
                discountableList.add(discountPriceInfo);
                amount.set(amount.get() - discountPriceInfo.getAmount());
            }

            if (amount.get() <= 0) {
                break;
            }
        }
        return discountableList;
    }

    /**
     * @param orderPriceInfo 欲退款/折扣的費用明細
     * @param amount         欲退款/折扣金額
     * @param category       設定退款/折扣類別
     */
    public OrderPriceInfo generateNegativeOrderPriceInfo(OrderPriceInfo orderPriceInfo, int amount, PriceInfoDefinition.PriceInfoCategory category) {
        int negativeAmt = 0;
        boolean isRefund = orderPriceInfo.getReceivedAmount() > 0 && orderPriceInfo.isPaid();
        if (isRefund) {
            negativeAmt = calculateRefundAvailableAmt(orderPriceInfo.getId());
        } else {
            negativeAmt = calculateDiscountAvailableAmt(orderPriceInfo.getId());
        }
        if (negativeAmt > 0) {
            OrderPriceInfo refundPriceInfo = new OrderPriceInfo();
            refundPriceInfo.setStage(orderPriceInfo.getStage());
            refundPriceInfo.setType(isRefund ? Refund.getCode() : Discount.getCode());
            refundPriceInfo.setReceivableDate(orderPriceInfo.getReceivableDate());
            refundPriceInfo.setLastPayDate(orderPriceInfo.getLastPayDate());
            refundPriceInfo.setAmount(Math.min(negativeAmt, amount));
            refundPriceInfo.setCategory(category);
            refundPriceInfo.setOrderNo(orderPriceInfo.getOrderNo());
            refundPriceInfo.setRefPriceInfoNo(orderPriceInfo.getId());
            if (isRefund) {
                if (StringUtils.isNotBlank(orderPriceInfo.getRecTradeId())) {
                    refundPriceInfo.setRecTradeId(orderPriceInfo.getRecTradeId());
                } else {
                    refundPriceInfo.setRemitAccountIds(orderPriceInfo.getRemitAccountIds());
                }
            }
            return refundPriceInfo;
        }
        return null;
    }

    /**
     * 更改出車時間後，異動可繳費期限
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateOrderPriceLastPayDay(Orders order, int diffDays) {
        List<OrderPriceInfo> orderPriceInfos = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(order.getOrderNo())).build())
            .stream().filter(orderPriceInfo -> !orderPriceInfo.isPaid()).collect(Collectors.toList());
        for (OrderPriceInfo orderPriceInfo : orderPriceInfos) {
            if (orderPriceInfo.getCategory() == ETag || orderPriceInfo.getCategory() == SecurityDeposit) {
                continue;
            }
            orderPriceInfo.setLastPayDate(DateUtil.convertToEndOfInstant(orderPriceInfo.getLastPayDate().plus(diffDays, DAYS)));
            orderPriceInfo.setReceivableDate(DateUtil.convertToStartOfInstant(orderPriceInfo.getReceivableDate().plus(diffDays, DAYS)));
        }
        orderPriceInfoRepository.saveAll(orderPriceInfos);
    }

    /**
     * 將退款資訊存入計價檔
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo storedRefundRecord(Integer orderPriceInfoId, Integer amount, @Nullable PriceInfoDefinition.PriceInfoCategory category) {
        orderPriceInfoRepository.findById(orderPriceInfoId)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        OrderPriceInfo refundOrderPriceInfo = storedRefundAndDiscountRecord(orderPriceInfoId, amount, category, Refund);
        return orderPriceInfoRepository.saveAndFlush(refundOrderPriceInfo);
    }

    /**
     * 將折扣資訊存入計價檔
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo storedDiscountRecord(Integer orderPriceInfoId, Integer amount, @Nullable PriceInfoDefinition.PriceInfoCategory category) {
        OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(orderPriceInfoId)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        OrderPriceInfo discountOrderPriceInfo = storedRefundAndDiscountRecord(orderPriceInfoId, amount, category, Discount);
        return orderPriceInfoRepository.save(discountOrderPriceInfo);
    }

    private OrderPriceInfo storedRefundAndDiscountRecord(Integer orderPriceInfoId, Integer amount, @Nullable PriceInfoDefinition.PriceInfoCategory category, PriceInfoDefinition.PriceInfoType type) {
        OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(orderPriceInfoId)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        OrderPriceInfo refundOrderPriceInfo = new OrderPriceInfo();
        BeanUtils.copyProperties(orderPriceInfo, refundOrderPriceInfo);
        refundOrderPriceInfo.setId(null);
        refundOrderPriceInfo.setAmount(amount);
        refundOrderPriceInfo.setReceivedAmount(0);
        refundOrderPriceInfo.setRefPriceInfoNo(orderPriceInfoId);
        refundOrderPriceInfo.setType(type.getCode());
        refundOrderPriceInfo.setRefPriceInfo(orderPriceInfo);
        refundOrderPriceInfo.setRecTradeId(orderPriceInfo.getRecTradeId());
        refundOrderPriceInfo.setPaymentId(null);
        Optional.ofNullable(category).ifPresent(refundOrderPriceInfo::setCategory);
        return refundOrderPriceInfo;
    }

    /**
     * 檢查付/退費用是否正確
     */
    public List<OrderPriceInfo> checkAmount(List<Integer> orderPriceInfoIds, int amount) {
        List<OrderPriceInfo> orderPriceInfos = orderPriceInfoRepository.findAllById(orderPriceInfoIds);
        if (orderPriceInfos.size() != orderPriceInfoIds.size()) {
            throw new SubscribeException(ACTUAL_PAYMENT_COUNT_NOT_MATCH_ORDER_PRICE_INFO);
        }
        orderPriceInfos.sort(Comparator.comparingInt(OrderPriceInfo::getType).reversed());
        for (OrderPriceInfo orderPriceInfo : orderPriceInfos) {
            if (orderPriceInfo.getType() == Pay.getCode()) {
                amount = amount - orderPriceInfo.getAmount();
            } else {
                amount = amount + orderPriceInfo.getAmount();
            }
        }
        if (amount != 0) {
            log.error("應收與實收不符合，金額:{} ,ids:{}", amount, orderPriceInfoIds);
            throw new SubscribeException(ORDER_PRICE_INFO_UNPAID_NOT_EQUALS_PAY);
        }
        return orderPriceInfos;
    }

    /**
     * 收到Queue收款資訊
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void receivePayment(PaymentQueue queue) throws JsonProcessingException {
        int amount = queue.getAmount();
        List<Integer> ids = objectMapper.readValue(queue.getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();
        List<OrderPriceInfo> orderPriceInfos = orderPriceInfoRepository.findAllById(ids);
        if (orderPriceInfos.size() != ids.size()) {
            throw new SubscribeException(ACTUAL_PAYMENT_COUNT_NOT_MATCH_ORDER_PRICE_INFO);
        }
        int receivedAmount = orderPriceInfos.stream().mapToInt(OrderPriceInfo::getActualReceivePrice).sum();
        if (receivedAmount > 0 && receivedAmount != amount) {
            Map<String, Object> map = new HashMap<>();
            map.put("Queue", queue);
            map.put("OrderPriceInfos", orderPriceInfos);
            mattermostServer.notify("請檢查款項是否重複收款", map, null);
        }
        orderPriceInfos.sort(Comparator.comparingInt(OrderPriceInfo::getType).reversed());
        for (OrderPriceInfo orderPriceInfo : orderPriceInfos) {
            if (orderPriceInfo.getType() == Pay.getCode()) {
                amount = amount - orderPriceInfo.getAmount();
            } else {
                amount = amount + orderPriceInfo.getAmount();
            }
            processOrderPriceInfoPayment(orderPriceInfo, queue);
        }
        if (amount != 0) {
            log.error("應收與實收不符合{}", queue);
        }
        orderPriceInfoRepository.saveAll(orderPriceInfos);
    }

    /**
     * 處理訂單費用資訊的支付邏輯
     * 1. 設置收款金額
     * 2. 設置交易序號
     * 3. 設置 payment 編號
     * 4. 執行訂單支付後的處理邏輯
     * 5. 處理 ETag 相關邏輯 (如果是 ETag 類型)
     *
     * @param orderPriceInfo    訂單費用資訊
     * @param tradeId           交易序號
     * @param paymentId         payment 編號
     * @param transactionNumber 銀行交易序號
     * @return 如果是 ETag 類型則回傳 ETag 處理結果，否則回傳 true
     */
    private boolean processOrderPriceInfoPaymentInternal(
        OrderPriceInfo orderPriceInfo,
        String tradeId,
        int paymentId,
        String transactionNumber
    ) {
        orderPriceInfo.setReceivedAmount(orderPriceInfo.getAmount());
        orderPriceInfo.setRecTradeId(tradeId);
        orderPriceInfo.setPaymentId(paymentId);
        self.afterPayOrderPriceInfo(orderService, carsService, orderPriceInfo);

        if (ETag.equals(orderPriceInfo.getCategory())) {
            return eTagService.processETag(orderPriceInfo, transactionNumber);
        }
        return true;
    }

    private void processOrderPriceInfoPayment(OrderPriceInfo orderPriceInfo, PaymentQueue paymentQueue) {
        boolean isSuccess = processOrderPriceInfoPaymentInternal(
            orderPriceInfo,
            paymentQueue.getTradeId(),
            paymentQueue.getPaymentId(),
            paymentQueue.getTransactionNumber()
        );

        if (!isSuccess) {
            log.error("ETag出還車記錄異常{}", paymentQueue);
        }
    }

    public void processOrderPriceInfoPayment(OrderPriceInfo orderPriceInfo, PaymentInfo paymentInfo) {
        boolean isSuccess = processOrderPriceInfoPaymentInternal(
            orderPriceInfo,
            paymentInfo.getTradeId(),
            paymentInfo.getPaymentId(),
            paymentInfo.getTransactionNumber()
        );

        if (!isSuccess) {
            log.error("ETag出還車記錄異常，tradeId: {}, paymentId: {}, transactionNumber: {}", paymentInfo.getTradeId(), paymentInfo.getPaymentId(), paymentInfo.getTransactionNumber());
        }
    }

    /**
     * 設定付款後處理額外款項明細流程
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void afterPayOrderPriceInfo(OrderService orderService, CarsService carsService, OrderPriceInfo orderPriceInfo) {
        try {
            if (MileageFee == orderPriceInfo.getCategory() && Pay.getCode() == orderPriceInfo.getType()) {
                Orders order = orderService.getOrder(orderPriceInfo.getOrderNo());
                carsService.updateCurrentMileage(order.getPlateNo(), orderPriceInfo.getInfoDetail().getEndMileage());
            }
        } catch (Exception e) {
            log.error("設定付款後處理額外資訊流程失敗,orderPriceInfo -> Id:{},category:{},type:{}", orderPriceInfo.getId(), orderPriceInfo.getCategory(), orderPriceInfo.getType(), e);
        }
    }

    /**
     * 付款後處理額外資訊流程
     */
    @Transactional
    public void afterPay(PaymentQueue queue,
                         OrderService orderService,
                         MattermostServer mattermostServer,
                         List<OrderPriceInfo> orderPriceInfoList) {
        try {
            Thread.sleep(2000);
            Orders order = orderService.getOrder(queue.getOrderId());
            // 當訂單為出車中且還車時間為空，且付款項目包含月費
            if ((order.getStatus() == DEPART.getStatus() || (order.getStatus() == OrderStatus.BOOKING.getStatus() && !order.getIsNewOrder())
                || order.getStatus() == OrderStatus.ARRIVE_NO_CLOSE.getStatus())
                && queue.getPaymentCategory() == PaymentCategory.PayAuth && queue.getAmount() > 0) {
                orderService.autoRecordAccountsCreateInvoice(order, orderPriceInfoList);
            }
            checkIsUnpaid(order);
            orderService.updateOrder(order);
        } catch (Exception e) {
            log.error("[自動收支登打失敗] ", e);
            mattermostServer.notify("[自動收支登打失敗] failed", new SingletonMap<>("Queue", queue), e);
        }
    }


    /**
     * 收到Queue退款資訊
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void receiveRefund(PaymentQueue queue) throws JsonProcessingException {
        int amount = queue.getAmount();

        if (StringUtils.isBlank(queue.getAdditionalData())) {
            return;
        }

        List<Integer> ids = objectMapper.readValue(queue.getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();
        List<OrderPriceInfo> orderPriceInfos = orderPriceInfoRepository.findAllById(ids);
        if (orderPriceInfos.size() != ids.size()) {
            throw new SubscribeException(ACTUAL_PAYMENT_COUNT_NOT_MATCH_ORDER_PRICE_INFO);
        }
        for (OrderPriceInfo orderPriceInfo : orderPriceInfos) {
            amount = amount - orderPriceInfo.getAmount();

            orderPriceInfo.setReceivedAmount(orderPriceInfo.getAmount());
            orderPriceInfo.setRefundId(queue.getRefundId());
            orderPriceInfo.setPaymentId(queue.getPaymentId());
        }
        if (amount != 0) {
            log.error("應退與實退不符合{}", queue);
        }
        orderPriceInfoRepository.saveAll(orderPriceInfos);
    }


    @Transactional(transactionManager = "mysqlTransactionManager")
    public void decideDiscount(String uid, String managerId, FineDiscountAgreeRequest request) {
        if (StringUtils.isNotBlank(uid)) {
            List<OrderPriceInfo> orderPriceInfoList = getByUid(uid);
            if (orderPriceInfoList.isEmpty()) {
                throw new SubscribeException(ORDER_PRICE_INFO_IS_CHANGE);
            }
            int discountSum = orderPriceInfoList.stream().mapToInt(OrderPriceInfo::getAmount).sum();
            if (discountSum != request.getDiscount()) {
                OrderPriceInfo opi = orderPriceInfoList.get(0);
                CarDropOffDiscountRequest carDropOffDiscountRequest = new CarDropOffDiscountRequest();
                carDropOffDiscountRequest.setDiscount(request.getDiscount());
                carDropOffDiscountRequest.setAgree(request.getIsAgree());
                carDropOffDiscountRequest.setReason(opi.getInfoDetail().getReason());
                carDropOffDiscountRequest.setCategory(opi.getCategory());
                carDropOffDiscountRequest.setOriginAmount(opi.getInfoDetail().getOriginAmount());
                carDropOffDiscountRequest.setDecideRemark(request.getDecideRemark());
                carDropOffDiscountRequest.setOriginalDiscount(opi.getInfoDetail().getDiscount());
                carDropOffDiscountRequest.setUid(uid);
                if (opi.getCategory().equals(PayLate)) {
                    carDropOffDiscountRequest.setPriceInfoPayId(opi.getRefPriceInfoNo());
                }
                deleteByUid(uid);
                orderService.dropOffCarDiscount(opi.getOrderNo(), carDropOffDiscountRequest, opi.getInfoDetail().getAdminId());
            } else {
                AtomicInteger discount = new AtomicInteger(request.getDiscount());
                orderPriceInfoList.forEach(opi -> {
                        PriceInfoDetail detail = opi.getInfoDetail();
                        if (detail.getIsAgree() != null) {
                            if (detail.getIsAgree()) {
                                throw new SubscribeException(ORDER_PRICE_INFO_ALREADY_AGREE);
                            } else {
                                throw new SubscribeException(ORDER_PRICE_INFO_ALREADY_DISAGREE);
                            }
                        }

                        opi.setAmount(discount.get() >= opi.getAmount() ? opi.getAmount() : discount.get());
                        discount.set(discount.get() - opi.getAmount());
                        if (discount.get() < 0) {
                            discount.set(0);
                        }
                        if (StringUtils.isNotBlank(managerId)) {
                            detail.setManagerId(managerId);
                        }
                        detail.setIsAgree(request.getIsAgree());
                        detail.setDecideRemark(request.getDecideRemark());
                        opi.setInfoDetail(detail);
                    }
                );
                orderPriceInfoRepository.saveAll(orderPriceInfoList);
                orderPriceInfoRepository.deleteAll(orderPriceInfoList.stream().filter(opi -> opi.getAmount() == 0).collect(Collectors.toList()));

            }
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public OrderPriceInfo get(Integer id) {
        return orderPriceInfoRepository.findById(id).orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<OrderPriceInfo> getByIds(List<Integer> ids) {
        return orderPriceInfoRepository.findAllById(ids);
    }

    public List<OrderPriceInfo> getByUid(String uid) {
        return orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().uid(uid).build());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void deleteByUid(String uid) {
        List<OrderPriceInfo> list = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().uid(uid).build());
        if (list.stream().anyMatch(opi -> opi.getReceivedAmount() > 0)) {
            throw new SubscribeException(ORDER_PRICE_INFO_ALREADY_DISCOUNTED_OR_REFUND);
        }
        orderPriceInfoRepository.deleteAll(list);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void delete(Integer id) {
        orderPriceInfoRepository.deleteById(id);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo addOrUpdate(OrderPriceInfo entity) {
        return orderPriceInfoRepository.save(entity);
    }

    public List<OrderPriceInfo> getMonthlyFeeUnpaidList(String orderNo) {
        return getPriceInfosByOrder(orderNo, MonthlyFee, Pay)
            .stream()
            .filter(orderPriceInfo -> orderPriceInfo.getStage() > 1
                && orderPriceInfo.getAmount() > orderPriceInfo.getReceivedAmount()
                && orderPriceInfo.getReceivableDate().isBefore(Instant.now()))
            .collect(Collectors.toList());
    }


    public List<YesChargingPoint> getYesChargingDefaultExample() {
        return configService.getYesChargingPointConfig();
    }

    /**
     * 可新增為額外費用的費用類別清單
     */
    public List<ExtraOrderPriceModel> getPriceInfoAddableAsExtraFee() {
        List<ExtraOrderPriceModel> list = new ArrayList<>();
        int sequence = 0;
        for (PriceInfoDefinition.PriceInfoCategory category : getCategoriesAddableAsExtraFee()) {
            list.add(new ExtraOrderPriceModel(++sequence, category));
        }
        return list;
    }

    /**
     * 可查詢額外費用明細的費用類別清單
     */
    public List<ExtraOrderPriceModel> getPriceInfoQueryableAsExtraFee() {
        List<ExtraOrderPriceModel> list = getPriceInfoAddableAsExtraFee();
        int sequence = list.size();

        // Add additional items for queryable priceInfo details
        list.add(new ExtraOrderPriceModel(++sequence, CarAccident));
        list.add(new ExtraOrderPriceModel(++sequence, Merchandise));

        return list;
    }

    /**
     * 檢查所有 OrderPriceInfo 是否皆為汽車用品類別或關聯費用編號為汽車用品費用 id
     */
    public boolean areAllPriceInfosMerchandiseRelated(List<OrderPriceInfo> orderPriceInfoList) {
        if (CollectionUtils.isEmpty(orderPriceInfoList)) {
            return false;
        }

        List<Integer> orderPriceInfoIds = orderPriceInfoList.stream().map(OrderPriceInfo::getId).collect(Collectors.toList());

        // 如果同時存在汽車用品和非汽車用品類別，拋出異常，例外：關聯費用編號為汽車用品費用 id
        Set<Boolean> isMerchandiseRelated = orderPriceInfoList.stream()
            .map(opi -> Merchandise == opi.getCategory() || orderPriceInfoIds.contains(opi.getRefPriceInfoNo()))
            .collect(Collectors.toSet());

        if (isMerchandiseRelated.size() > 1) {
            throw new SubscribeException(ORDER_PRICE_INFO_CATEGORY_MIXED);
        }

        return isMerchandiseRelated.iterator().next();
    }

    /**
     * 取得訂單所有符合額外費用項目 && 車損費用 OrderPriceInfos
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<OrderPriceInfo> getFeePriceInfos(String orderNo) {
        return orderPriceInfoRepository.findAllGeneralCategoryByOrderNo(orderNo);
    }
}
