package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.EContractTemplateRepository;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ConditionOrderSource;
import com.carplus.subscribe.enums.FileTypeEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.feign.CrsClient;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.crs.CarBaseInfoQueryReq;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchPage;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.presign.GcsGetDownloadUrlReq;
import com.carplus.subscribe.model.presign.GcsGetUploadUrlReq;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.request.econtract.TemplateCreateReq;
import com.carplus.subscribe.model.request.econtract.TemplateUpdateReq;
import com.carplus.subscribe.model.request.econtract.TemplateValidateReq;
import com.carplus.subscribe.model.response.econtract.EContractTemplateGetResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.GoSmartServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.AccessPermission;
import org.apache.pdfbox.pdmodel.encryption.StandardProtectionPolicy;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.carplus.subscribe.constant.CarPlusConstant.PLATE_FORM_CODE;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_NOT_FOUND;

@Service
@Slf4j
public class EContractLogic {

    @Autowired
    private EContractService eContractTemplateService;

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private CarsService carsService;

    @Autowired
    private CarModelService carModelService;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private CrsClient crsClient;

    @Autowired
    private EContractTemplateRepository eContractTemplateRepository;


    /**
     * 建立合約範本
     */
    public EContractTemplate createTemplate(TemplateCreateReq req, String memberId) {
        TemplateValidateReq templateValidateReq = new TemplateValidateReq();
        templateValidateReq.setConditionCarBu(req.getConditionCarBu());
        templateValidateReq.setConditionCarBrand(req.getConditionCarBrand());
        templateValidateReq.setConditionCarState(req.getConditionCarState());
        templateValidateReq.setConditionOrderMonth(req.getConditionOrderMonth());
        templateValidateReq.setConditionOrderSource(req.getConditionOrderSource());
        templateValidateReq.setConditionCarPriceStart(req.getConditionCarPriceStart());
        templateValidateReq.setConditionCarPriceEnd(req.getConditionCarPriceEnd());
        templateValidateReq.setDisclaimerFee(req.isDisclaimerFee());
        templateValidateReq.setSeaLandCar(req.isSeaLandCar());
        templateValidateReq.setConditionCarModel(req.getConditionCarModel());
        if (eContractTemplateRepository.isTemplateIsExists(templateValidateReq)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_EXISTS);
        }
        String templateCode = eContractTemplateService.generateTemplateCode();
        String versionId = eContractTemplateService.generateVersionId(templateCode);

        EContractTemplate econtractTemplate = generateTemplate(req, memberId, templateCode, versionId);
        eContractTemplateService.createTemplate(econtractTemplate);


        return econtractTemplate;
    }

    /**
     * 合約範本升版本
     */
    public EContractTemplate upgradeTemplate(TemplateCreateReq req, String memberId) {
        TemplateValidateReq templateValidateReq = new TemplateValidateReq();
        templateValidateReq.setConditionCarBu(req.getConditionCarBu());
        templateValidateReq.setConditionCarBrand(req.getConditionCarBrand());
        templateValidateReq.setConditionCarState(req.getConditionCarState());
        templateValidateReq.setConditionOrderMonth(req.getConditionOrderMonth());
        templateValidateReq.setConditionOrderSource(req.getConditionOrderSource());
        templateValidateReq.setConditionCarPriceStart(req.getConditionCarPriceStart());
        templateValidateReq.setConditionCarPriceEnd(req.getConditionCarPriceEnd());
        templateValidateReq.setDisclaimerFee(req.isDisclaimerFee());
        templateValidateReq.setSeaLandCar(req.isSeaLandCar());
        templateValidateReq.setConditionCarModel(req.getConditionCarModel());
        List<EContractTemplate> eContractTemplates = eContractTemplateRepository.getTemplateList(templateValidateReq);
        if (eContractTemplates.isEmpty()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS);
        }

        String versionId = eContractTemplateService.generateVersionId(eContractTemplates.get(0).getTemplateCode());
        EContractTemplate econtractTemplate = generateTemplate(req, memberId, eContractTemplates.get(0).getTemplateCode(), versionId);
        eContractTemplateService.createTemplate(econtractTemplate);

        return econtractTemplate;
    }

    /**
     * 修改電子合約範本
     */
    public EContractTemplate updateTemplate(Integer templateId, String memberId, TemplateUpdateReq req) {
        EContractTemplate updateTemplate = eContractTemplateService.findById(templateId);

        if (Objects.isNull(updateTemplate)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS);
        }

        // EContractTemplate
        BeanUtils.copyProperties(req, updateTemplate);
        updateTemplate.setUpdateUser(memberId);
        updateTemplate.setSeaLandCar(req.isSeaLandCar());
        updateTemplate.setConditionCarModel(req.getConditionCarModel());
        updateTemplate.setDisclaimerFee(req.isDisclaimerFee());
        updateTemplate.setVersionId(eContractTemplateService.generateVersionId(updateTemplate.getTemplateCode()));

        eContractTemplateService.updateTemplate(updateTemplate);

        return updateTemplate;
    }

    /**
     * 檢視電子合約範本
     */
    public EContractTemplateGetResponse getTemplate(Integer templateId) {

        EContractTemplateGetResponse econtractTemplateGetResponse = new EContractTemplateGetResponse();

        EContractTemplate econtractTemplate = eContractTemplateService.findById(templateId);
        if (Objects.isNull(econtractTemplate)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS);
        }

        BeanUtils.copyProperties(econtractTemplate, econtractTemplateGetResponse);

        return econtractTemplateGetResponse;
    }

    /**
     * 產生合約範本
     */
    public EContractTemplate generateTemplate(TemplateCreateReq req, String memberId, String templateCode, String versionId) {

        EContractTemplate econtractTemplate = new EContractTemplate();

        // 電子合約範本資料
        econtractTemplate.setTemplateCode(templateCode);
        econtractTemplate.setVersionId(versionId);
        econtractTemplate.setTemplateNameSales(req.getTemplateNameSales());
        econtractTemplate.setTemplateNameCust(req.getTemplateNameCust());
        econtractTemplate.setConditionOrderSource(Objects.requireNonNull(ConditionOrderSource.codeOfValue(req.getConditionOrderSource())).getCode());
        econtractTemplate.setConditionOrderMonth(req.getConditionOrderMonth());
        econtractTemplate.setConditionCarBu(BuIdEnum.ofEnum(Integer.valueOf(req.getConditionCarBu())).getCode().toString());
        econtractTemplate.setConditionCarBrand(req.getConditionCarBrand());
        econtractTemplate.setConditionCarState(CarDefine.CarState.valueOf(req.getConditionCarState()).toString());
        econtractTemplate.setConditionCarPriceStart(req.getConditionCarPriceStart());
        econtractTemplate.setConditionCarPriceEnd(req.getConditionCarPriceEnd());
        econtractTemplate.setEnableDate(req.getEnableDate());
        econtractTemplate.setReviseMemo(req.getReviseMemo());
        econtractTemplate.setCreateUser(memberId);
        econtractTemplate.setUpdateUser(memberId);
        econtractTemplate.setUploadFileId(req.getUploadFileId());
        econtractTemplate.setDisplayFilename(req.getDisplayFilename());
        econtractTemplate.setSeaLandCar(req.isSeaLandCar());
        econtractTemplate.setDisclaimerFee(req.isDisclaimerFee());
        econtractTemplate.setConditionCarModel(req.getConditionCarModel());
        return econtractTemplate;
    }

    /**
     * 取得GCS下載URL
     */
    public String getTemplateDownloadUrl(String fileName) {
        // 呼叫 goSmart 服務取得上傳 req
        GcsGetUploadUrlReq gcsGetUploadUrlReq = createGcsGetUploadUrlReq(FileTypeEnum.PDF);
        // 呼叫 goSmart 服務取得下載 url
        GcsGetDownloadUrlReq gcsGetDownloadUrlReq = createGcsGetDownloadUrlReq(fileName);
        log.info("getTemplateDownloadUrl, call goSmart api, GcsGetDownloadUrlReq={}", gcsGetUploadUrlReq);
        GcsUrlRes gcsDownloadUrlRes = goSmartServer.getGcsDownloadUrl(gcsGetDownloadUrlReq);
        List<GcsUrlRes.TypeResponse> signedUrls = gcsDownloadUrlRes.getSignedUrls();
        if (CollectionUtils.isEmpty(signedUrls)) {
            log.error("getTemplateDownloadUrl, call goSmart getGcsDownloadUrl fail");
            throw new RuntimeException("查無資料");
        }
        String downloadUrl = signedUrls.get(0).getSignedUrl();
        log.info("getTemplateDownloadUrl, downloadUrl={}", downloadUrl);

        // 回傳下載 url
        return downloadUrl;
    }

    /**
     * 建立 GCS 檔案下載 url 請求
     *
     * @param filename GCS 檔案名稱
     */
    public GcsGetDownloadUrlReq createGcsGetDownloadUrlReq(String filename) {
        return GcsGetDownloadUrlReq.builder()
            .source(HeaderDefine.SystemKind.SUB)
            .filePath(CarPlusConstant.ECONTRACT_CODE)
            .isTemp(Boolean.FALSE)
            .fileNames(Collections.singletonList(filename))
            .build();
    }

    /**
     * 建立 GCS 檔案下載 url
     *
     * @param fileTypeEnum 檔案格式
     */
    public GcsGetUploadUrlReq createGcsGetUploadUrlReq(FileTypeEnum fileTypeEnum) {
        GcsGetUploadUrlReq.TypeCount typeCount = GcsGetUploadUrlReq.TypeCount.builder()
            .mediaType(fileTypeEnum.getMediaType())
            .quantity(1)
            .build();
        return GcsGetUploadUrlReq.builder()
            .source(HeaderDefine.SystemKind.SUB)
            .filePath(CarPlusConstant.ECONTRACT_CODE)
            .isTemp(Boolean.FALSE)
            .requestList(Collections.singletonList(typeCount))
            .build();
    }

    /**
     * 統一格式
     */
    public String regularPath(String path) {
        String patternString = "(/SUB/ECON/(.*))";
        String formatString;

        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(path);

        if (matcher.find()) {
            formatString = matcher.group(2);

        } else {
            formatString = path;
        }

        return formatString;
    }

    /**
     * 加密 PDF 檔案
     */
    public byte[] encryptGcsFile(String downloadUrl, Integer acctId) throws Exception {
        byte[] encryptedContent;
        String secretKey;

        try {
            AuthUser user = authServer.getUserWithRetry(acctId);
            if (Objects.nonNull(user)) {
                secretKey = user.getLoginId();
            } else {
                throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_ACCOUNT_NOT_EXISTS);
            }
            encryptedContent = encrypt(downloadUrl, secretKey);
        } catch (Exception e) {
            log.error("EContractTemplateLogic encryptGcsFile Error: {}", e.getMessage());
            throw e;
        }

        return encryptedContent;
    }

    private byte[] encrypt(String downloadUrl, String secretKey) throws Exception {
        InputStream inputStream = new URL(downloadUrl).openStream();
        byte[] inputByte = IOUtils.toByteArray(inputStream);
        PDDocument document = Loader.loadPDF(inputByte);

        // 設定加密參數
        AccessPermission accessPermission = new AccessPermission();
        accessPermission.setCanPrint(true);
        accessPermission.setCanModify(false);
        accessPermission.setCanExtractContent(true);

        StandardProtectionPolicy protectionPolicy = new StandardProtectionPolicy(secretKey, secretKey, accessPermission);
        protectionPolicy.setEncryptionKeyLength(128); // 設定加密key長度
        document.protect(protectionPolicy);

        // 轉為 byte[]
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        document.save(byteArrayOutputStream);
        byte[] encryptedPdfBytes = byteArrayOutputStream.toByteArray();

        document.close();

        return encryptedPdfBytes;
    }

    /**
     * 透過車輛資源取得車輛所屬
     */
    public String getConditionCarBu(String brandCode) {
        Result<CarBaseInfoSearchPage> result = new Result<>();
        Cars cars;
        String conditionCarBu = "";
        String plateNo;

        CarModel carModel = carModelService.findByBrandCode(brandCode);

        if (Objects.nonNull(carModel)) {
            cars = carsService.findByCarModelCode(carModel.getCarModelCode());
            plateNo = cars.getPlateNo();
        } else {
            return conditionCarBu;
        }

        if (StringUtils.isNotBlank(plateNo)) {
            result = crsClient.getCars(PLATE_FORM_CODE, HeaderDefine.SystemKind.SUB,
                CarBaseInfoQueryReq.builder().limit(10).plateNoList(Collections.singletonList(cars.getPlateNo())).build());
        }
        if (result.getStatusCode() == 0) {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = result.getData().getPage().getList().stream().filter(car -> car.getPlateNo().equals(plateNo)).findAny().orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
            conditionCarBu = Integer.toString(carBaseInfoSearchResponse.getBuId().equals(BuIdEnum.lRental.getCode()) ? carBaseInfoSearchResponse.getBuId() : BuIdEnum.subscribe.getCode());
        }

        return conditionCarBu;
    }

}
