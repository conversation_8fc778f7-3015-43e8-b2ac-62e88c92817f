package com.carplus.subscribe.service;

import carplus.common.enums.etag.ETagFlow;
import carplus.common.enums.etag.ETagPayFlow;
import carplus.common.enums.etag.ETagRentFuncId;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.DealerOrderRepository;
import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.dao.OrderRepository;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.etag.ETagInfoRequest;
import com.carplus.subscribe.model.etag.ETagResponse;
import com.carplus.subscribe.model.etag.ETagResult;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.server.FinanceServer;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.ETagPayment.Free;
import static com.carplus.subscribe.enums.ETagPayment.OFFICIAL;
import static com.carplus.subscribe.model.order.DealerOrderPriceInfoRequest.getDealerOrderPriceInfoRequest;

@Slf4j
@Service
public class ETagService {

    private final FinanceServer financeServer;
    private final StationService stationService;
    private final OrderService orderService;
    private final OrderRepository orderRepository;
    private final DealerOrderService dealerOrderService;
    private final DealerOrderPriceInfoService dealerOrderPriceInfoService;
    private final DealerOrderRepository dealerOrderRepository;
    private final EtagInfoRepository etagInfoRepository;
    private final NotifyService notifyService;
    private final CrsService crsService;
    private final ConfigService configService;
    @Autowired
    @Lazy
    private PriceInfoService priceInfoService;

    public ETagService(
        FinanceServer financeServer,
        StationService stationService,
        OrderService orderService,
        OrderRepository orderRepository,
        DealerOrderService dealerOrderService,
        DealerOrderPriceInfoService dealerOrderPriceInfoService,
        DealerOrderRepository dealerOrderRepository,
        EtagInfoRepository etagInfoRepository,
        NotifyService notifyService,
        CrsService crsService,
        ConfigService configService) {
        this.financeServer = financeServer;
        this.stationService = stationService;
        this.orderService = orderService;
        this.orderRepository = orderRepository;
        this.dealerOrderService = dealerOrderService;
        this.dealerOrderPriceInfoService = dealerOrderPriceInfoService;
        this.dealerOrderRepository = dealerOrderRepository;
        this.etagInfoRepository = etagInfoRepository;
        this.notifyService = notifyService;
        this.crsService = crsService;
        this.configService = configService;
    }

    /**
     * 透過訂單號碼拿取EtagInfo清單
     */
    public List<EtagInfoResponse> getETagIntoByOrderNo(String orderNo) {
        return etagInfoRepository.getETagInfosByOrderNo(orderNo).stream().map(EtagInfoResponse::new).collect(Collectors.toList());
    }

    /**
     * 手動設定EtagInfo金額
     */
    public EtagInfoResponse manualSetEtagAmt(String orderNo, String memberId, ETagInfoRequest request) {
        ETagInfo etagInfo = etagInfoRepository.findById(request.getEtagInfoId()).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ETAG_INFO_NOT_FUND));
        if (!Objects.equals(orderNo, etagInfo.getOrderNo())) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND);
        }
        if (Objects.equals(etagInfo.getETagPayFlow(), ETagPayFlow.CONFIRMING.getCode()) || Objects.equals(etagInfo.getETagPayFlow(), ETagPayFlow.CONFIRMING_SMS.getCode())) {
            if (request.getDepartDate() == null || request.getReturnDate() == null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_RENT_TIME_NOT_GIVEN);
            }
            if (request.getDepartDate().isAfter(request.getReturnDate())) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_RENT_AFTER_RETURN);
            }
            etagInfo.setDepartDate(request.getDepartDate());
            etagInfo.setReturnDate(request.getReturnDate());
        }
        OrderPriceInfo etagOrderPriceInfo = Optional.of(etagInfo).map(ETagInfo::getOrderPriceInfo).orElse(null);
        int receiveAmt = Optional.ofNullable(etagOrderPriceInfo).map(OrderPriceInfo::getReceivedAmount).orElse(0);
        if (receiveAmt > 0 && Optional.ofNullable(request.getPayabled()).orElse(true)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_CAN_NOT_CHANGE_TO_UNPAYABLE);
        }
        etagInfo.setPayabled(Optional.of(request).map(ETagInfoRequest::getPayabled).orElse(true));
        etagInfo.setETagAmt(request.getPaidETagAmt());
        etagInfo.setETagPayment(request.getETagPayment());
        etagInfo.setExistETagDetail(request.isExistETagDetail());
        etagInfo.setRemark(request.getRemark());
        etagInfo.setNotPayableReason(request.getNotPayableReason());
        etagInfo.setCreditBankAuto(request.getCreditBankAuto());
        etagInfo.setEdcBatchNo(request.getEdcBatchNo());
        if (etagOrderPriceInfo == null) {
            PriceInfoDetail detail = new PriceInfoDetail();
            detail.setAdminId(memberId);
            etagOrderPriceInfo = priceInfoService.generateEtagOrderPriceInfo(etagInfo, orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND)));
            etagOrderPriceInfo.setAmount(etagInfo.getETagAmt());
            etagOrderPriceInfo.setInfoDetail(detail);
            priceInfoService.addOrUpdate(etagOrderPriceInfo);
            etagInfo.setOrderPriceInfoId(etagOrderPriceInfo.getId());
        } else {
            if (Objects.isNull(etagOrderPriceInfo.getInfoDetail())) {
                PriceInfoDetail detail = new PriceInfoDetail();
                detail.setAdminId(memberId);
                etagOrderPriceInfo.setInfoDetail(detail);
            } else {
                etagOrderPriceInfo.getInfoDetail().setAdminId(memberId);
            }
            etagOrderPriceInfo.setAmount(etagInfo.getETagAmt());
            etagOrderPriceInfo.setUpdator(memberId); // 設定 updator 用於更新的價格資訊
            priceInfoService.addOrUpdate(etagOrderPriceInfo);
        }
        if (etagInfo.getETagAmt() == 0 || !etagInfo.isPayabled()) {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE_NOT_NEED_PAY.getCode());
        } else if (!ETagPayFlow.CONFIRMING.getCode().equals(etagInfo.getETagPayFlow()) && !ETagPayFlow.CONFIRMING_SMS.getCode().equals(etagInfo.getETagPayFlow())) {
            etagInfo.setETagPayFlow(ETagPayFlow.CONFIRMING.getCode());
        }
        if (etagInfo.getReturnDate() == null) {
            Orders order = orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND));
            etagInfo.setReturnDate(Optional.ofNullable(order.getEndDate()).orElse(Instant.now()));
        }
        etagInfoRepository.save(etagInfo);
        priceInfoService.checkIsUnpaid(orderService.getOrder(orderNo));
        return new EtagInfoResponse(etagInfo);
    }

    /**
     * 手動設定EtagInfo金額 for DealerOrder
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public EtagInfoResponse manualSetDealerOrderEtagAmt(String orderNo, String memberId, ETagInfoRequest request) {
        ETagInfo etagInfo = etagInfoRepository.findById(request.getEtagInfoId()).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ETAG_INFO_NOT_FUND));
        if (!Objects.equals(orderNo, etagInfo.getOrderNo())) {
            throw new SubscribeException(SubscribeHttpExceptionCode.DEALER_ORDER_NOT_FOUND);
        }
        if (Objects.equals(etagInfo.getETagPayFlow(), ETagPayFlow.CONFIRMING.getCode()) || Objects.equals(etagInfo.getETagPayFlow(), ETagPayFlow.CONFIRMING_SMS.getCode())) {
            if (request.getDepartDate() == null || request.getReturnDate() == null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_RENT_TIME_NOT_GIVEN);
            }
            if (request.getDepartDate().isAfter(request.getReturnDate())) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_RENT_AFTER_RETURN);
            }
            etagInfo.setDepartDate(request.getDepartDate());
            etagInfo.setReturnDate(request.getReturnDate());
        }
        Optional<DealerOrderPriceInfo> etagPriceInfo = dealerOrderPriceInfoService.getEtagPriceInfo(orderNo);
        int receiveAmt = etagPriceInfo.map(DealerOrderPriceInfo::getTransactionAmt).orElse(0);
        if (receiveAmt > 0 && Optional.ofNullable(request.getPayabled()).orElse(true)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_CAN_NOT_CHANGE_TO_UNPAYABLE);
        }

        etagInfo.setPayabled(Optional.of(request).map(ETagInfoRequest::getPayabled).orElse(true));
        etagInfo.setETagAmt(request.getPaidETagAmt());
        etagInfo.setETagPayFlow(ETagPayFlow.DONE.getCode());
        etagInfo.setETagPayment(request.getETagPayment());
        etagInfo.setExistETagDetail(request.isExistETagDetail());
        etagInfo.setRemark(request.getRemark());
        etagInfo.setNotPayableReason(request.getNotPayableReason());
        etagInfo.setCreditBankAuto(request.getCreditBankAuto());
        etagInfo.setEdcBatchNo(request.getEdcBatchNo());

        if (etagPriceInfo.isPresent()) {
            etagPriceInfo.get().setTransactionAmt(etagInfo.getETagAmt());
            dealerOrderPriceInfoService.addOrUpdate(etagPriceInfo.get());
        } else {
            dealerOrderPriceInfoService.createDealerOrderPriceInfo(getDealerOrderPriceInfoRequest(etagInfo));
        }

        if (etagInfo.getETagAmt() == 0 || !etagInfo.isPayabled()) {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE_NOT_NEED_PAY.getCode());
        }
        if (etagInfo.getReturnDate() == null) {
            DealerOrder dealerOrder = dealerOrderService.getOrder(etagInfo.getOrderNo());
            etagInfo.setReturnDate(Optional.ofNullable(dealerOrder.getReturnDate()).orElse(Instant.now()));
        }
        etagInfoRepository.save(etagInfo);
        return new EtagInfoResponse(etagInfo);
    }

    /**
     * 車號判斷是否為合法通路 by finance
     *
     * @param plateNo 車號
     * @update 2023-12-12因目前皆為合法通路，且配合2023-12-16機房遷移，避免受影響，故先依率回復True
     */
    public boolean isValid(@NonNull String plateNo) {
        return financeServer.isValidWithETag(plateNo);
    }

    /**
     * 透過訂單號碼產生新Etag出車
     */
    public ETagInfo rentCarForNewStage(String orderNo) {
        Orders order = orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND));
        rentCarForNewStage(order, order.getPlateNo(), order.getDepartMemberId());
        return getLatestNotReturnETagInfo(order, true);
    }

    /**
     * 遠通出車
     */
    public void rentCarForNewStage(@NonNull Orders order, String plateNo, String adminId) {
        ETagInfo eTag = Optional.ofNullable(getLatestNotReturnETagInfo(order, true))
            .orElseGet(() -> generateLatestETagInfo(order));
        if (eTag.getReturnDate() != null) {
            eTag = generateLatestETagInfo(order);
        }
        rentCar(eTag, plateNo, adminId);
        eTag.setOrderNo(order.getOrderNo());
        etagInfoRepository.save(eTag);
        orderRepository.save(order);
        eTagDepartFailProcess(eTag, plateNo, adminId);
    }

    /**
     * 透過訂單號碼初次/異動Etag出車
     */
    public ETagInfo rentCar(String orderNo, String memberId) {

        if (orderService.isCarPlusOrder(orderNo)) {
            // 查詢格上訂單
            Optional<Orders> orderOptional = orderRepository.findById(orderNo);
            if (orderOptional.isPresent()) {
                Orders order = orderOptional.get();
                rentCar(order, order.getPlateNo(), order.getDepartMemberId());
                return getLatestNotReturnETagInfo(order, true);
            }
        }

        // 再嘗試查詢經銷商訂單
        Optional<DealerOrder> dealerOrderOptional = dealerOrderRepository.findById(orderNo);
        if (dealerOrderOptional.isPresent()) {
            DealerOrder dealerOrder = dealerOrderOptional.get();
            rentCarForDealerOrder(dealerOrder, Optional.ofNullable(memberId).orElseGet(() -> configService.getSubscribeConfig().getSubscribeDefaultMemberId()));
            return getLatestNotReturnETagInfo(dealerOrder, true);
        }

        // 兩種訂單都不存在則拋出異常
        throw new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND);
    }

    /**
     * 遠通出車
     */
    public void rentCar(@NonNull Orders order, String plateNo, String adminId) {
        ETagInfo eTag = Optional.ofNullable(getLatestNotReturnETagInfo(order, true))
            .orElseGet(() -> generateLatestETagInfo(order));
        rentCar(eTag, plateNo, adminId);
        eTag.setOrderNo(order.getOrderNo());
        etagInfoRepository.save(eTag);
        orderRepository.save(order);
        eTagDepartFailProcess(eTag, plateNo, adminId);
    }

    /**
     * 遠通出車
     */
    public void rentCar(@NonNull ETagInfo etag, String plateNo, String adminId) {
        try {

            if (etag.getReturnDate() != null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_ALREADY_DEPART);
            }
            ETagRentFuncId funcId = ETagRentFuncId.DEPART_INSERT;
            etag.setIsValid(isValid(plateNo));
            if (etag.getIsValid()) {
                if (Objects.equals(etag.getIsSuccess(), Boolean.TRUE) || Objects.equals(ETagFlow.DEPART_SUCCESS.getCode(), etag.getETagFlow())) {
                    funcId = ETagRentFuncId.DEPART_UPDATE;
                }
                boolean eTagSuccess = rentCar(etag, plateNo, funcId.getFuncId(), adminId);
                // 若遠通出車成功或是過往無出車過資訊 或是過往非出車成功，則異動Etag資訊
                if (eTagSuccess || (etag.getIsSuccess() == null || etag.getETagFlow() == null) || !(Objects.equals(etag.getIsSuccess(), Boolean.TRUE) || Objects.equals(etag.getETagFlow(), ETagFlow.DEPART_SUCCESS.getCode()))) {
                    etag.setIsSuccess(eTagSuccess);
                    if (eTagSuccess) {
                        saveETagFlow(etag, ETagFlow.DEPART_SUCCESS);
                    } else {
                        saveETagFlow(etag, ETagFlow.DEPART_ETAG_FAIL);
                        int departErrorCode = Optional.ofNullable(etag.getDepartFailCode()).orElse(0);
                        switch (departErrorCode) {

                            case 49: // 車號輸入有誤，請重新輸入
                                notifyService.notifyEtagDepartFailNotApplyETag(Collections.singletonList(plateNo));
                                break;
                            case 2105: // 非租賃車號
                                notifyService.notifyEtagDepartFailNotInCarGroup(Collections.singletonList(plateNo));
                                break;
                            case 2025: // 已有相同租車資料，不可再次新增相同的租車資料
                            case 2124: // 租車時間已經重疊於先前出租區間
                            case 2125: // 查無租還車資料
                            case 2134: // 契約編號重複
                            default:
                        }
                    }
                }
            } else {
                saveETagFlow(etag, ETagFlow.DEPART_NOT_VALID);
            }
        } catch (Exception ex) {
            saveETagFlow(etag, ETagFlow.DEPART_ETAG_FAIL);
            log.error("rentCar error", ex);
        } finally {
            etagInfoRepository.save(etag);
        }
    }

    /**
     * 遠通出車
     */
    private boolean rentCar(@NonNull ETagInfo etagInfo, @NonNull String plateNo, @NonNull String funId, String adminMemberId) {
        ETagResponse result = financeServer.rentWithETag(etagInfo, plateNo, funId, adminMemberId);
        String errorMsg = "";
        Map<String, Object> alert = Maps.newHashMap();
        alert.put("statusCode", result.getStatusCode());
        alert.put("plateNo", plateNo);
        alert.put("funId", funId);
        alert.put("stage", etagInfo.getStage());
        boolean response = true;

        if (result.getStatusCode() != 0) {
            errorMsg = String.format("訂單: %s, 呼叫 etag 出車服務失敗: %s", etagInfo.getOrderNo(), result.getMessage());
            log.error(errorMsg);
            etagInfo.setDepartFail(result.getMessage());
        }

        if (result.getData() != null && "F".equals(result.getData().getStatus())) {
            errorMsg = String.format("訂單: %s, etag 出車失敗原因: %s", etagInfo.getOrderNo(), result.getData().getDescription());
            log.error(errorMsg);
            alert.put("status", "F");
            etagInfo.setDepartFail(Optional.of(result).map(Result::getData).map(ETagResult::getDescription).orElse(null));
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            response = false;
        }
        return response;
    }

    /**
     * 遠通出車 for DealerOrder
     */
    public void rentCarForDealerOrder(@NonNull DealerOrder dealerOrder, String adminId) {
        ETagInfo eTag = Optional.ofNullable(getLatestNotReturnETagInfo(dealerOrder, true))
            .orElseGet(() -> generateLatestETagInfo(dealerOrder));
        rentCar(eTag, dealerOrder.getPlateNo(), adminId);
        eTag.setOrderNo(dealerOrder.getOrderNo());
        etagInfoRepository.save(eTag);
        dealerOrderRepository.save(dealerOrder);
        eTagDepartFailProcess(eTag, dealerOrder.getPlateNo(), adminId);
    }

    public ETagInfo processAndUpdateETagInfoForCarReturn(Orders order, String plateNo, String adminId, String returnStationCode, ETagInfo etag) {
        if (ETagFlow.DEPART_SUCCESS.getCode().equals(etag.getETagFlow()) || oldOrderETagDepartSuccess(etag)) {
            try {
                boolean queryAndSetSuccess = query(order, plateNo, adminId, etag);
                if (queryAndSetSuccess) {
                    saveETagFlow(etag, ETagFlow.RETURN_SUCCESS);
                    if (etag.getETagAmt() == 0) {
                        saveETagPayFlow(etag, ETagPayFlow.DONE_NOT_NEED_PAY);
                    } else {
                        // 前端顯示 etag 還車成功，請依系統顯示收取費用
                        saveETagPayFlow(etag, ETagPayFlow.PENDING);
                    }
                } else {
                    saveETagFlow(etag, ETagFlow.RETURN_FAIL);
                    customEditEtagAmt(etag);
                }
                // etag 還車、etag 查詢
                etag.setIsSuccess(queryAndSetSuccess);

            } catch (Exception ex) {
                saveETagFlow(etag, ETagFlow.RETURN_FAIL);
                customEditEtagAmt(etag);
                log.error("returnCar error", ex);
            }
            // 出車就沒打eTag, 車籍為非格上車, 且還車站所為經銷商據點
        } else if (isNonCarPlusFleetReturnedToDealer(returnStationCode, etag)) {
            etag.setETagAmt(0);
            saveETagFlow(etag, ETagFlow.RETURN_NOT_VALID_AND_RETURN_DEALER);
            saveETagPayFlow(etag, ETagPayFlow.DONE_NOT_NEED_PAY);
        } else {
            // DEPART FAIL -> RETURN OTHER
            customEditEtagAmt(etag);
            saveETagFlow(etag, ETagFlow.RETURN_OTHER);

        }
        etag.setReturnDate(Instant.now());
        etagInfoRepository.save(etag);
        return etag;
    }

    /**
     * 出車就沒打eTag, 車籍為非格上車, 且還車站所為經銷商據點
     */
    private boolean isNonCarPlusFleetReturnedToDealer(String returnStationCode, ETagInfo etag) {
        return (ETagFlow.DEPART_NOT_VALID.getCode().equals(etag.getETagFlow()) || ETagFlow.DEPART_ETAG_FAIL.getCode().equals(etag.getETagFlow()))
            && CarPlusFleet.N.equals(etag.getCarPlusFleet())
            && returnDealer(returnStationCode);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo returnCar(String orderNo) {
        Orders order = orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND));
        returnCar(order, order.getPlateNo(), order.getDepartMemberId(), order.getContract().getMainContract().getReturnStationCode());
        return getLatestNotReturnETagInfo(order, true);
    }

    /**
     * 遠通還車
     * isSuccess: 對遠通出車是否成功
     * isValid: 受否為格上車隊
     * isPassReturnCar: 是否略過對遠通還車
     * -> 1. 出車異常時
     * -> 2. 出車異常, 但簡訊付款流程會把isSuccess = true, 所以單判斷isSuccess不準確
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo returnCar(@NonNull Orders order, String plateNo, String adminId, String returnStationCode) {

        ETagInfo eTag = Optional.ofNullable(getLatestNotReturnETagInfo(order, true))
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ETAG_PAID_CAN_NOT_MODIFY_RETURN_TIME));
        return processAndUpdateETagInfoForCarReturn(order, plateNo, adminId, returnStationCode, eTag);
    }

    /**
     * 遠通還車
     * isSuccess: 對遠通出車是否成功
     * isValid: 受否為格上車隊
     * isPassReturnCar: 是否略過對遠通還車
     * -> 1. 出車異常時
     * -> 2. 出車異常, 但簡訊付款流程會把isSuccess = true, 所以單判斷isSuccess不準確
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo returnCar(@NonNull DealerOrder dealerOrder, String plateNo, String adminId, String returnStationCode) {

        ETagInfo eTag = Optional.ofNullable(getLatestNotReturnETagInfo(dealerOrder, true))
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ETAG_PAID_CAN_NOT_MODIFY_RETURN_TIME));
        if (ETagFlow.DEPART_SUCCESS.getCode().equals(eTag.getETagFlow()) || oldOrderETagDepartSuccess(eTag)
            || eTag.getETagFlow() >= ETagFlow.RETURN_SUCCESS.getCode()) {
            try {
                boolean returnAndSetSuccess = returnCar(dealerOrder, plateNo, adminId, eTag, returnStationCode);
                if (returnAndSetSuccess) {
                    returnAndSetSuccess = dealerOrderQuery(dealerOrder, eTag, dealerOrder.getPlateNo(), adminId).isReturnAndSetSuccess();
                }
                if (returnAndSetSuccess) {
                    saveETagFlow(eTag, ETagFlow.RETURN_SUCCESS);
                    saveETagPayFlow(eTag, ETagPayFlow.DONE);
                } else {
                    saveETagFlow(eTag, ETagFlow.RETURN_FAIL);
                    customEditEtagAmt(eTag);
                }
                // etag 還車、etag 查詢
                eTag.setIsSuccess(returnAndSetSuccess);

            } catch (Exception ex) {
                saveETagFlow(eTag, ETagFlow.RETURN_FAIL);
                customEditEtagAmt(eTag);
                log.error("returnCar error", ex);
            }
            // 出車就沒打eTag, 車籍為非格上車, 且還車站所為經銷商據點
        } else if (isNonCarPlusFleetReturnedToDealer(returnStationCode, eTag)) {
            eTag.setETagAmt(0);
            saveETagFlow(eTag, ETagFlow.RETURN_NOT_VALID_AND_RETURN_DEALER);
            saveETagPayFlow(eTag, ETagPayFlow.DONE_NOT_NEED_PAY);
        } else {
            // DEPART FAIL -> RETURN OTHER
            customEditEtagAmt(eTag);
            saveETagFlow(eTag, ETagFlow.RETURN_OTHER);

        }
        eTag.setReturnDate(Instant.now());
        etagInfoRepository.save(eTag);
        return eTag;
    }

    /**
     * 還車
     */
    private boolean returnCar(@NonNull ETagInfo etagInfo, @NonNull String funId, String plateNo, String adminId, String returnStationCode) {
        ETagResponse result = financeServer.returnWithETag(etagInfo, funId, plateNo, adminId, returnStationCode);
        String errorMsg = "";
        Map<String, Object> alert = Maps.newHashMap();
        alert.put("statusCode", result.getStatusCode());
        alert.put("plateNo", plateNo);
        alert.put("funId", funId);
        boolean response = true;

        if (result.getStatusCode() != 0) {
            errorMsg = String.format("訂單: %s, 呼叫 etag 還車服務失敗: %s", etagInfo.getOrderNo(), result.getMessage());
            log.error(errorMsg);
        }

        ETagResult eTagResult = result.getData();
        if (eTagResult != null && "F".equals(eTagResult.getStatus())) {
            errorMsg = String.format("訂單: %s, etag 還車失敗原因: %s", etagInfo.getOrderNo(), eTagResult.getDescription());
            log.error(errorMsg);
            etagInfo.setReturnFail(eTagResult.getDescription());
            alert.put("status", "F");
        }

        if (StringUtils.isNotBlank(errorMsg)) {
            response = false;
        }
        return response;
    }

    public boolean returnCar(@NonNull DealerOrder dealerOrder, String plateNo, String adminId, ETagInfo etagInfo, String returnStationCode) {
        return returnCar(dealerOrder, plateNo, adminId, etagInfo, returnStationCode, null, 0);
    }

    /**
     * 還車
     */
    public boolean returnCar(@NonNull DealerOrder dealerOrder, String plateNo, String adminId, ETagInfo etagInfo, String returnStationCode, ETagRentFuncId funcId, int count) {
        if (funcId == null) {
            if (etagInfo.getETagFlow() >= ETagFlow.RETURN_SUCCESS.getCode()) {
                funcId = ETagRentFuncId.RETURN_UPDATE;
            } else {
                funcId = ETagRentFuncId.RETURN_INSERT;
            }
        }
        ETagResponse result = financeServer.returnWithETag(etagInfo, funcId.getFuncId(), plateNo, adminId, returnStationCode);
        if (result.getStatusCode() != 0) {
            log.error("訂單: {}, 呼叫 etag 查詢服務失敗: {}", dealerOrder.getOrderNo(), result.getMessage());

            return false;
        }

        if (result.getData() != null) {
            ETagResult eTagResponse = result.getData();
            Integer etagOriErrCode = etagInfo.getReturnFailCode();
            if ("F".equals(eTagResponse.getStatus())) {
                log.error("訂單: {}, etag 查詢失敗原因: {}", dealerOrder.getOrderNo(), result.getData().getDescription());
                etagInfo.setReturnFail(eTagResponse.getDescription());
                // 2125查無租還車資料
                // 2027已有相同還車資料，不可再次新增相同的還車資料
                // 針對兩種情況做遠通出還車處理
                if (Objects.equals(etagInfo.getReturnFailCode(), 2027) && !Objects.equals(etagOriErrCode, 2027) && count <= 1) {
                    count++;
                    returnCar(dealerOrder, plateNo, adminId, etagInfo, returnStationCode, ETagRentFuncId.RETURN_UPDATE, count);
                } else if (Objects.equals(etagInfo.getReturnFailCode(), 2125) && !Objects.equals(etagOriErrCode, 2125) && count <= 1) {
                    count++;
                    returnCar(dealerOrder, plateNo, adminId, etagInfo, returnStationCode, ETagRentFuncId.RETURN_INSERT, count);
                }
                return false;
            }
            return true;
        }

        log.error("訂單: {}, etag 查詢失敗無法計算金額", dealerOrder.getOrderNo());

        return false;
    }

    /**
     * 出車中換車流程 - 遠通還車資料異動確認
     * isSuccess: 對遠通出車是否成功
     * isValid: 受否為格上車隊
     * isPassReturnCar: 是否略過對遠通還車
     * -> 1. 出車異常時
     * -> 2. 出車異常, 但簡訊付款流程會把isSuccess = true, 所以單判斷isSuccess不準確
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo returnCarModifyForReplace(@NonNull Orders order, String plateNo, String adminId, String returnStationCode) {
        ETagInfo eTag = Optional.ofNullable(getLatestNotReturnETagInfo(order, true))
            .orElseThrow(() -> {
                log.error("replaceCar - return car modify failed");
                return new SubscribeException(SubscribeHttpExceptionCode.ETAG_PAID_CAN_NOT_MODIFY_RETURN_TIME);
            });
        return processAndUpdateETagInfoForCarReturn(order, plateNo, adminId, returnStationCode, eTag);
    }

    public EtagInfoResponse dealerOrderQuery(DealerOrder dealerOrder, @NonNull ETagInfo etagInfo, String plateNo, String adminId) {
        boolean isSuccess = query(dealerOrder, plateNo, adminId, etagInfo);
        if (isSuccess) {
            etagInfo.setDealerAndETagAccount(isDealerAndETagAccount(plateNo));
        }
        return new EtagInfoResponse(etagInfo, isSuccess);
    }

    /**
     * 出車中換車 - 汰換車遠通還車確認
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo closeOutCarCheck(Orders order) {
        ETagInfo eTag = closeOut(order, order.getPlateNo(), order.getDepartMemberId(), order.getContract().getMainContract().getReturnStationCode());
        OrderPriceInfo orderPriceInfo = priceInfoService.addOrUpdate(priceInfoService.generateEtagOrderPriceInfo(eTag, order));
        eTag.setOrderPriceInfoId(orderPriceInfo.getId());
        eTag.setRemark("出車中換車");
        etagInfoRepository.save(eTag);
        return eTag;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo closeOut(@NonNull Orders order, String plateNo, String adminId, String returnStationCode) {
        ETagInfo etagInfo = Optional.ofNullable(getLatestNotReturnETagInfo(order, false))
            .orElseThrow(() -> {
                log.error("replaceCar - return car check failed");
                return new BadRequestException("找不到eTag出車資料，不可還車");
            });

        // 格上車隊合法通路
        if (ETagFlow.RETURN_SUCCESS.getCode().equals(etagInfo.getETagFlow()) || oldOrderETagDepartSuccess(etagInfo)) {
            returnCar(etagInfo, ETagRentFuncId.RETURN_DELETE.getFuncId(), plateNo, adminId, returnStationCode);
        }

        etagInfo.setLockETagAmt(true);
        return etagInfoRepository.save(etagInfo);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo closeCar(String orderNo) {
        Orders order = orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND));
        ETagInfo eTag = close(order, order.getPlateNo(), order.getDepartMemberId(), order.getContract().getMainContract().getReturnStationCode(), true);
        OrderPriceInfo orderPriceInfo = priceInfoService.addOrUpdate(priceInfoService.generateEtagOrderPriceInfo(eTag, order));
        eTag.setOrderPriceInfoId(orderPriceInfo.getId());
        etagInfoRepository.save(eTag);
        return eTag;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo close(@NonNull Orders order, String plateNo, String adminId, String returnStationCode, boolean isReturnCar) {
        return close(order, plateNo, adminId, returnStationCode, isReturnCar, false);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo close(@NonNull Orders order, String plateNo, String adminId, String returnStationCode, boolean isReturnCar, boolean isLegalOperation) {
        ETagInfo etagInfo = Optional.ofNullable(getLatestNotReturnETagInfo(order, false))
            .orElseThrow(() -> new BadRequestException("找不到eTag出車資料，不可還車"));
        // 若執行法務作業，強制設定 etag 還車時間為訂單實際還車時間
        if (isLegalOperation && order.getEndDate() != null) {
            etagInfo.setReturnDate(order.getEndDate());
        }
        return close(etagInfo, plateNo, adminId, returnStationCode, isReturnCar);
    }

    /**
     * 完成繳費 after SP s_I02_01_ContractSave_Return
     * isSuccess: 對遠通出車是否成功
     * isValid: 受否為格上車隊
     * isPassReturnCar: 是否略過對遠通還車
     * -> 1. 出車異常時
     * -> 2. 出車異常, 但簡訊付款流程會把isSuccess = true, 所以單判斷isSuccess不準確
     */
    private ETagInfo close(ETagInfo etagInfo, String plateNo, String adminId, String returnStationCode, boolean isReturnCar) {
        // 格上車隊合法通路
        if (ETagFlow.RETURN_SUCCESS.getCode().equals(etagInfo.getETagFlow()) || oldOrderETagDepartSuccess(etagInfo)) {
            chkReturnCar(etagInfo, returnStationCode, isReturnCar);
            // 完成 etag 還車繳費
            returnCar(etagInfo, ETagRentFuncId.RETURN_DELETE.getFuncId(), plateNo, adminId, returnStationCode);
        }
        etagInfo.setLockETagAmt(true);
        return etagInfoRepository.save(etagInfo);
    }

    /**
     * 完成繳費 after SP s_I02_01_ContractSave_Return
     * isSuccess: 對遠通出車是否成功
     * isValid: 受否為格上車隊
     * isPassReturnCar: 是否略過對遠通還車
     * -> 1. 出車異常時
     * -> 2. 出車異常, 但簡訊付款流程會把isSuccess = true, 所以單判斷isSuccess不準確
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo close(@NonNull DealerOrder dealerOrder, String plateNo, String adminId, String returnStationCode, boolean isReturnCar) {
        ETagInfo etagInfo = getLatestNotReturnETagInfo(dealerOrder, false);
        if (etagInfo == null) {
            // 找不到 etag 出車資料，直接返回 null，不進行後續操作
            return null;
        }
        return close(etagInfo, plateNo, adminId, returnStationCode, isReturnCar);
    }

    public List<ETagInfo> getByOrderPriceInfoIdIn(List<Integer> orderPriceInfoIds) {
        return etagInfoRepository.getByOrderPriceInfoIdIn(orderPriceInfoIds);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public boolean processETag(OrderPriceInfo orderPriceInfo, String transactionNumber) {
        ETagInfo etagInfo = etagInfoRepository.getEtagInfoByOrderPriceId(orderPriceInfo.getId());
        if (null == etagInfo) {
            return false;
        }
        if (orderPriceInfo.getReceivedAmount() > 0 && orderPriceInfo.getAmount() > 0) {
            etagInfo.setPayabled(true);
        }

        ETagPayment eTagPayment = etagInfo.isPayabled() ? OFFICIAL : Free;
        etagInfo.setPaidETagAmt(orderPriceInfo.getAmount());
        etagInfo.setETagPayment(ETagPayment.Sms);
        etagInfo.setTransactionNumber(transactionNumber);
        if (eTagPayment.equals(Free)) {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE_NOT_NEED_PAY.getCode());
        } else {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE.getCode());
        }

        etagInfo.setLockETagAmt(true);
        etagInfoRepository.save(etagInfo);
        return true;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public boolean processETagRemit(OrderPriceInfo orderPriceInfo) {
        ETagInfo etagInfo = etagInfoRepository.getEtagInfoByOrderPriceId(orderPriceInfo.getId());
        if (null == etagInfo) {
            return false;
        }
        if (orderPriceInfo.getAmount() > 0) {
            etagInfo.setPayabled(true);
        }

        ETagPayment eTagPayment = etagInfo.isPayabled() ? OFFICIAL : Free;
        etagInfo.setPaidETagAmt(orderPriceInfo.getAmount());
        etagInfo.setETagPayment(ETagPayment.Sms);
        if (eTagPayment.equals(Free)) {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE_NOT_NEED_PAY.getCode());
        } else {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE.getCode());
        }
        etagInfo.setRemark("匯款收費");
        etagInfo.setLockETagAmt(true);
        etagInfoRepository.save(etagInfo);
        return true;
    }

    /**
     * 查詢
     */
    public boolean query(@NonNull IOrder orders, String plateNo, String adminId, ETagInfo etagInfo) {
        ETagResponse result = financeServer.queryWithETag(etagInfo, plateNo, adminId);
        if (result.getStatusCode() != 0) {
            log.error("訂單: {}, 呼叫 etag 查詢服務失敗: {}", orders.getOrderNo(), result.getMessage());

            return false;
        }

        if (result.getData() != null) {
            ETagResult eTagResponse = result.getData();
            if ("F".equals(eTagResponse.getStatus())) {
                log.error("訂單: {}, etag 查詢失敗原因: {}", orders.getOrderNo(), result.getData().getDescription());
                etagInfo.setReturnFail(eTagResponse.getDescription());
                return false;
            }

            Double rentAmount = eTagResponse.getRentAmount();
            Double offsetAmount = eTagResponse.getOffsetAmount();
            if (rentAmount != null && offsetAmount != null) {
                int rent = (int) Math.round(rentAmount);
                etagInfo.setFetcAmt(rent);
                int offset = (int) Math.round(offsetAmount);
                etagInfo.setFetcOffSetAmt(offset);

                // 金額各自四捨五入再相減
                // RENT_AMOUNT-ABS(OFFSET_AMOUNT)
                int eTagAmt = rent - Math.abs(offset);
                etagInfo.setETagAmt(eTagAmt);
                etagInfo.setDealerAndETagAccount(isDealerAndETagAccount(plateNo));

                return true;
            }
        }

        log.error("訂單: {}, etag 查詢失敗無法計算金額", orders.getOrderNo());

        return false;
    }


    private boolean isDealerAndETagAccount(String plateNo) {
        // 確認是否為經銷商
        return crsService.getCar(plateNo) != null;
    }

    /**
     * 共用的 ETagInfo 過濾與排序邏輯
     */
    private ETagInfo getLatestNotReturnETagInfo(List<ETagInfo> etagInfos, boolean ignoreLocked, Instant departDate) {
        return etagInfos.stream()
            .filter(etag -> !(ignoreLocked && etag.isLockETagAmt())) // 若 ignoreLocked 為 true，則排除已鎖定編輯的 ETagInfo
            .max(Comparator.comparing(ETagInfo::getStage)) // 找出 stage 最大的 ETagInfo
            .map(latestETagInfo -> {
                // 補從srental轉過來的訂單
                if (latestETagInfo.getDepartDate() == null) {
                    latestETagInfo.setDepartDate(departDate);
                }
                return latestETagInfo;
            })
            .orElse(null);
    }

    /**
     * 取得最新未還車的ETagInfo
     */
    public ETagInfo getLatestNotReturnETagInfo(Orders order, boolean ignoreLocked) {
        List<ETagInfo> etagInfos = getETagInfosByOrderNo(order);
        return getLatestNotReturnETagInfo(etagInfos, ignoreLocked, order.getStartDate());
    }

    /**
     * 取得最新未還車的ETagInfo for DealerOrder
     */
    public ETagInfo getLatestNotReturnETagInfo(DealerOrder dealerOrder, boolean ignoreLocked) {
        List<ETagInfo> etagInfos = etagInfoRepository.getETagInfosByOrderNo(dealerOrder.getOrderNo());
        return getLatestNotReturnETagInfo(etagInfos, ignoreLocked, dealerOrder.getDepartDate());
    }

    /**
     * 共用的 ETagInfo 過濾與排序邏輯
     */
    private ETagInfo getLatestReturnETagInfo(List<ETagInfo> etagInfos) {
        return Optional.ofNullable(etagInfos)
            .flatMap(list -> list.stream()
                .filter(e -> e.isLockETagAmt() || ETagPayFlow.DONE_NOT_NEED_PAY.getCode().equals(e.getETagPayFlow()))
                .max(Comparator.comparing(ETagInfo::getStage)))
            .orElse(null);
    }

    public ETagInfo getLatestReturnETagInfo(Orders order) {
        return getLatestReturnETagInfo(getETagInfosByOrderNo(order));
    }

    public ETagInfo getLatestReturnETagInfo(DealerOrder dealerOrder) {
        return getLatestReturnETagInfo(etagInfoRepository.getETagInfosByOrderNo(dealerOrder.getOrderNo()));
    }

    /**
     * 產生最新的Etag資訊
     */
    public ETagInfo generateLatestETagInfo(Orders order) {
        Instant departDate = order.getStartDate();
        int stage = 1;
        List<ETagInfo> etagInfoList = getETagInfosByOrderNo(order);
        if (etagInfoList == null || etagInfoList.isEmpty()) {
            if (!order.getIsNewOrder()) {
                Orders previousOrder = orderRepository.getPreviousOrders(order.getOrderNo());
                if (previousOrder != null) {
                    ETagInfo etagInfo = generateLatestETagInfo(previousOrder);
                    etagInfo.setOrderNo(order.getOrderNo());
                    etagInfo.setOrder(order);
                    etagInfo.setStage(stage);
                    return etagInfo;
                }
            }
        } else {
            ETagInfo etagInfo = etagInfoList.stream().sorted(Comparator.comparing(ETagInfo::getStage).reversed()).collect(Collectors.toList()).get(0);
            if (etagInfo.getReturnDate() == null) {
                throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_RENT_CAR_NEXT_ORDER_FAIL);
            }
            departDate = etagInfo.getReturnDate().plusSeconds(1);
            stage = etagInfoList.size() + 1;
        }
        return ETagInfo.builder()
            .departDate(departDate)
            .orderNo(order.getOrderNo())
            .stage(stage)
            .build();
    }

    /**
     * 產生最新的Etag資訊 for DealerOrder
     */
    public ETagInfo generateLatestETagInfo(DealerOrder dealerOrder) {
        return ETagInfo.builder()
            .departDate(dealerOrder.getDepartDate())
            .orderNo(dealerOrder.getOrderNo())
            .stage(1)
            .uploaded(true)
            .build();
    }

    public void saveETagFlow(ETagInfo etagInfo, ETagFlow etagFlow) {
        etagInfo.setETagFlow(etagFlow.getCode());
    }

    public void saveETagPayFlow(ETagInfo etagInfo, ETagPayFlow etagPayFlow) {
        etagInfo.setETagPayFlow(etagPayFlow.getCode());
    }

    /**
     * 相容上線之前舊的已打遠通出車的eTag訂單判斷狀態
     */
    private boolean oldOrderETagDepartSuccess(ETagInfo etagInfo) {
        log.info("oldOrderETagDepartSuccess 判斷舊訂單ETag出車成功: {}, isPassReturnCar: [{}], isSuccess: [{}], isValid: [{}], ETagFlow: [{}]",
            etagInfo.getOrderNo(),
            etagInfo.isPassReturnCar(),
            etagInfo.getIsSuccess(),
            etagInfo.getIsValid(),
            etagInfo.getETagFlow());
        return BooleanUtils.isFalse(etagInfo.isPassReturnCar())
            && BooleanUtils.isTrue(etagInfo.getIsSuccess())
            && BooleanUtils.isTrue(etagInfo.getIsValid());
    }

    // 手動登打金額
    public void customEditEtagAmt(ETagInfo etag) {
        etag.setIsSuccess(false);
        // 已人工設定過金額，不再異動
        if ((Objects.equals(etag.getETagFlow(), ETagPayFlow.CONFIRMING.getCode()) || Objects.equals(etag.getETagFlow(), ETagPayFlow.CONFIRMING_SMS.getCode()))
            && Optional.ofNullable(etag.getETagAmt()).orElse(0) > 0) {
            return;
        }
        // 打遠通失敗,etagAmt=null,讓其手動登打金額
        etag.setETagAmt(null);
        saveETagPayFlow(etag, ETagPayFlow.CONFIRMING);
    }

    private boolean returnDealer(String returnStationCode) {
        Stations returnStation = stationService.findByStationCode(returnStationCode);
        // 確認還車站是否為經銷商
        return returnStation.getStationCategory() == StationDefine.StationCategory.DEALER;
    }

    /**
     * 還車前檢查
     */
    public void chkReturnCar(@NonNull ETagInfo etagInfo, String returnStationCode, boolean isReturnCar) {
        if (etagInfo.isPaymentCompleted()) {
            return;
        }
        if (isReturnCar && ETagPayFlow.PENDING.getCode().equals(etagInfo.getETagPayFlow())) {
            throw new BadRequestException("eTag 付款流程為待確認未完成, 請確認收支是否登打正確");
        }
        if (ETagPayFlow.CONFIRMING.getCode().equals(etagInfo.getETagPayFlow())) {
            throw new BadRequestException("eTag 付款流程為待收款未完成, 請確認收支是否登打");
        }
        if (ETagPayFlow.CONFIRMING_SMS.getCode().equals(etagInfo.getETagPayFlow())) {
            throw new BadRequestException("簡訊付款流程未完成");
        }
        // 格上車隊合法通路 打 eTag 成功
        if (ETagFlow.RETURN_SUCCESS.getCode().equals(etagInfo.getETagFlow())
            || (BooleanUtils.isTrue(etagInfo.getIsSuccess()) && BooleanUtils.isTrue(etagInfo.getIsValid()))) {
            // 是否為經銷商門市出格上車隊
            boolean returnDealer = returnDealer(returnStationCode);
            int eTagAmt = Optional.ofNullable(etagInfo.getETagAmt()).orElse(0);
            int paidETagAmt = Optional.ofNullable(etagInfo.getPaidETagAmt()).orElse(0);

            if (isReturnCar) {
                // 經銷商出車，eTag 付款需完成
                if (returnDealer && eTagAmt > 0 && eTagAmt != paidETagAmt) {
                    throw new BadRequestException("etag 付款未完成!");
                }

                // 門市出車, 未填寫實付金額(=null)
                if (!returnDealer && eTagAmt > 0 && Objects.isNull(etagInfo.getPaidETagAmt())) {
                    throw new BadRequestException("etag 付款確認未完成!");
                }

                // 門市出車, 要收款, 應付金額!=實付金額
                if (!returnDealer && eTagAmt > 0 && etagInfo.isPayabled() && eTagAmt != paidETagAmt) {
                    throw new BadRequestException("etag 付款未完成!");
                }
                // 大於 0 才檢核
                if (eTagAmt > 0) {
                    this.validate(etagInfo);
                }
            }
        }

        // 1. 非格上車隊合法通路
        // 2. 是格上車隊合法通路 打etag失敗
        // 不會有遠通金額(eTagAmt=null), 只能依靠 paidETagAmt
        // 且未填寫實付金額(=null)
        if ((BooleanUtils.isFalse(etagInfo.getIsValid()) || BooleanUtils.isFalse(etagInfo.getIsSuccess())) && Objects.isNull(etagInfo.getPaidETagAmt())) {

            throw new BadRequestException("etag 付款確認未完成!");
        }
        if (Objects.isNull(etagInfo.getETagPayFlow())) {
            etagInfo.setETagPayFlow(ETagPayFlow.DONE.getCode());
            etagInfoRepository.save(etagInfo);
        }
    }

    /**
     * ETagInfo編輯邏輯檢查
     */
    public void validate(@NonNull ETagInfo entity) {
        Integer paidETagAmt = entity.getPaidETagAmt();
        if (Objects.isNull(paidETagAmt)) {
            throw new BadRequestException("eTag 實收金額不可為空");
        }

        boolean payabled = entity.isPayabled();
        // 要收款
        if (payabled) {
            if (paidETagAmt == 0) {
                throw new BadRequestException("eTag 實收金額不可為0");
            }
            ETagPayment eTagPayment = entity.getETagPayment();
            if (Objects.isNull(eTagPayment)) {
                throw new BadRequestException("eTag 收款方式不可為空");
            }
            if (eTagPayment == ETagPayment.Credit) {
                Integer creditBankAuto = entity.getCreditBankAuto();
                if (Objects.isNull(creditBankAuto)) {
                    throw new BadRequestException("eTag 刷卡方式不可為空");
                }
                ChargeType chargeType = ChargeType.of(creditBankAuto);
                if (chargeType != null && PaymentType.EDC == chargeType.getPaymentType() && StringUtils.isBlank(entity.getEdcBatchNo())) {
                    throw new BadRequestException("eTag EDC結帳批號不可為空");
                }
            }
        }

        // 不收款
        if (!payabled) {
            NotPayableReason notPayableReason = entity.getNotPayableReason();
            if (notPayableReason == null) {
                throw new BadRequestException("eTag 不收款原因不可為空");
            }
            if (NotPayableReason.Other == notPayableReason && StringUtils.isBlank(entity.getRemark())) {
                throw new BadRequestException("eTag 備註不可為空");
            }
        }
    }

    public void eTagDepartFailProcess(ETagInfo etagInfo, String plateNo, String adminId) {
        //
        if (!Objects.equals(etagInfo.getETagFlow(), ETagFlow.DEPART_SUCCESS.getCode())) {
            int errorCode = Optional.ofNullable(etagInfo.getDepartFailCode()).orElse(0);

            switch (errorCode) {
                case 2025: // 已有相同租車資料，不可再次新增相同的租車資料(有其他遠通契約尚未還車)
                case 49: // 車號輸入有誤，請重新輸入
                case 2105: // 非租賃車號
                case 2124: // 租車時間已經重疊於先前出租區間
                    if (etagInfo.getStage() != 1) {
                        ETagInfo preEtagInfo = etagInfoRepository.getETagInfosByOrderNo(etagInfo.getOrderNo()).stream().filter(e -> e.getStage() + 1 == etagInfo.getStage()).findAny().orElse(null);
                        if (preEtagInfo != null && etagInfo.getDepartDate().isBefore(preEtagInfo.getReturnDate())) {
                            etagInfo.setDepartDate(preEtagInfo.getReturnDate());
                            rentCar(etagInfo, plateNo, adminId);
                            etagInfoRepository.save(etagInfo);
                        }
                    }
                    break;
                case 2125: // 查無租還車資料
                case 2134: // 契約編號重複
                default:

            }
        }

    }

    /**
     * 根據訂單編號取得所有ETag資訊
     */
    public List<ETagInfo> getETagInfosByOrderNo(Orders order) {
        return etagInfoRepository.getETagInfosByOrderNo(order.getOrderNo());
    }
}
